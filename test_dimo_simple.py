#!/usr/bin/env python3
"""
Simple DiMO Test
Test basic DiMO functionality without full training setup.
"""

import torch
import sys
import os
from os import path as osp

# Add current directory to path
sys.path.insert(0, osp.dirname(osp.abspath(__file__)))

def test_student_network():
    """Test DiMO student network."""
    print("Testing DiMO student network...")
    
    try:
        from polarfree.archs.dimo_student_arch import dimo_student
        
        # Create student network
        student = dimo_student(
            input_dim=256,
            prior_dim=256,
            output_dim=256,
            hidden_dim=256,
            num_layers=3,
            group=4,
            patch_expansion=0.5,
            channel_expansion=2
        )
        
        print(f"Student network created with {sum(p.numel() for p in student.parameters())} parameters")
        
        # Test forward pass
        batch_size, seq_len = 1, 16
        input_features = torch.randn(batch_size, seq_len, 256)
        prior_features = torch.randn(batch_size, seq_len, 256)
        
        with torch.no_grad():
            output = student(input_features, prior_features)
        
        print(f"Forward pass successful: input {input_features.shape} -> output {output.shape}")
        assert output.shape == (batch_size, seq_len, 256), f"Expected shape {(batch_size, seq_len, 256)}, got {output.shape}"
        print("✓ Student network test passed")
        return True
        
    except Exception as e:
        print(f"✗ Student network test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_loss_functions():
    """Test DiMO loss functions."""
    print("\nTesting DiMO loss functions...")
    
    try:
        from polarfree.losses.dimo_loss import DiMODistillationLoss
        
        # Test distillation loss
        distill_loss = DiMODistillationLoss(loss_type='mse')
        
        batch_size, seq_len, dim = 1, 16, 256
        student_output = torch.randn(batch_size, seq_len, dim)
        teacher_output = torch.randn(batch_size, seq_len, dim)
        
        loss_value = distill_loss(student_output, teacher_output)
        print(f"Distillation loss computed: {loss_value.item():.6f}")
        assert loss_value.item() >= 0, "Loss should be non-negative"
        
        print("✓ Loss functions test passed")
        return True
        
    except Exception as e:
        print(f"✗ Loss functions test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_model_registry():
    """Test model registry."""
    print("\nTesting model registry...")
    
    try:
        from basicsr.utils.registry import MODEL_REGISTRY
        
        # Check if DiMO is registered
        if 'DiMO' in MODEL_REGISTRY._obj_map:
            print("✓ DiMO model is registered")
            return True
        else:
            print("✗ DiMO model is not registered")
            print(f"Available models: {list(MODEL_REGISTRY._obj_map.keys())}")
            return False
        
    except Exception as e:
        print(f"✗ Model registry test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_arch_registry():
    """Test architecture registry."""
    print("\nTesting architecture registry...")
    
    try:
        from basicsr.utils.registry import ARCH_REGISTRY
        
        # Check if dimo_student is registered
        if 'dimo_student' in ARCH_REGISTRY._obj_map:
            print("✓ dimo_student architecture is registered")
            return True
        else:
            print("✗ dimo_student architecture is not registered")
            print(f"Available architectures: {list(ARCH_REGISTRY._obj_map.keys())}")
            return False
        
    except Exception as e:
        print(f"✗ Architecture registry test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_loss_registry():
    """Test loss registry."""
    print("\nTesting loss registry...")
    
    try:
        from basicsr.utils.registry import LOSS_REGISTRY
        
        # Check if DiMO losses are registered
        dimo_losses = ['DiMODistillationLoss', 'DiMOCombinedLoss', 'DiMOConsistencyLoss']
        registered_losses = []
        
        for loss_name in dimo_losses:
            if loss_name in LOSS_REGISTRY._obj_map:
                registered_losses.append(loss_name)
        
        if registered_losses:
            print(f"✓ DiMO losses registered: {registered_losses}")
            return True
        else:
            print("✗ No DiMO losses are registered")
            print(f"Available losses: {list(LOSS_REGISTRY._obj_map.keys())}")
            return False
        
    except Exception as e:
        print(f"✗ Loss registry test failed: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests."""
    print("=" * 60)
    print("DiMO Simple Test Suite")
    print("=" * 60)
    
    tests = [
        test_student_network,
        test_loss_functions,
        test_model_registry,
        test_arch_registry,
        test_loss_registry
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! DiMO implementation is working correctly.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)

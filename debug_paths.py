#!/usr/bin/env python3
"""
Debug script to check path configuration
"""

import os
from os import path as osp
import sys
import pprint

# Add current directory to path
sys.path.insert(0, osp.dirname(osp.abspath(__file__)))

from polarfree.utils.options import parse_options

def main():
    # Parse options
    root_path = osp.abspath(osp.join(__file__, osp.pardir))
    opt, args = parse_options(root_path, is_train=True)
    opt['root_path'] = root_path
    
    print("=" * 60)
    print("Path Configuration Debug")
    print("=" * 60)
    
    print(f"Root path: {root_path}")
    print(f"Experiment name: {opt['name']}")
    
    print("\nPath dictionary:")
    pprint.pprint(opt['path'])
    
    print("\nLogger configuration:")
    pprint.pprint(opt['logger'])
    
    print("\nChecking for None values in path:")
    for key, value in opt['path'].items():
        if value is None:
            print(f"  {key}: {value} (None)")
        else:
            print(f"  {key}: {value}")
    
    print("\nChecking for None values in logger:")
    for key, value in opt['logger'].items():
        if value is None:
            print(f"  {key}: {value} (None)")
        elif isinstance(value, dict):
            print(f"  {key}:")
            for subkey, subvalue in value.items():
                if subvalue is None:
                    print(f"    {subkey}: {subvalue} (None)")
                else:
                    print(f"    {subkey}: {subvalue}")
        else:
            print(f"  {key}: {value}")

if __name__ == '__main__':
    main()

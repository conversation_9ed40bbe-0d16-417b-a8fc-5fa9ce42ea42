#!/usr/bin/env python3
"""
DiMO Testing Script
Test a trained DiMO (Distilling Masked Diffusion Models into One-step Generator) model
for polarization image enhancement.
"""

import argparse
import cv2
import glob
import logging
import numpy as np
import os
import torch
from os import path as osp

from basicsr.data import build_dataloader, build_dataset
from basicsr.models import build_model as build_basicsr_model
from basicsr.utils import get_root_logger, get_time_str, make_exp_dirs, scandir
from basicsr.utils.options import copy_opt_file, dict2str
from polarfree.utils.options import parse_options

from polarfree.models import build_model


def main():
    # Parse options
    root_path = osp.abspath(osp.join(__file__, osp.pardir))
    opt, args = parse_options(root_path, is_train=False)
    opt['root_path'] = root_path

    # Mkdir for experiments and logger
    make_exp_dirs(opt)
    log_file = osp.join(opt['path']['log'], f"test_{opt['name']}_{get_time_str()}.log")
    logger = get_root_logger(logger_name='basicsr', log_level=logging.INFO, log_file=log_file)
    logger.info(dict2str(opt))

    # Create test dataset and dataloader
    test_loaders = []
    for phase, dataset_opt in sorted(opt['datasets'].items()):
        test_set = build_dataset(dataset_opt)
        test_loader = build_dataloader(
            test_set, dataset_opt, num_gpu=opt['num_gpu'], dist=opt['dist'], sampler=None, seed=opt['manual_seed'])
        logger.info(f'Number of test images in {dataset_opt["name"]}: {len(test_set)}')
        test_loaders.append(test_loader)

    # Create model
    model = build_model(opt)

    for test_loader in test_loaders:
        test_set_name = test_loader.dataset.opt['name']
        logger.info(f'Testing {test_set_name}...')
        model.validation(test_loader, current_iter=opt['name'], tb_logger=None, save_img=opt['val']['save_img'])


def test_single_image(model_path, input_path, output_path, config_path=None):
    """Test a single image with DiMO model.
    
    Args:
        model_path (str): Path to the trained DiMO model
        input_path (str): Path to input polarization images directory
        output_path (str): Path to save the enhanced image
        config_path (str): Path to the config file (optional)
    """
    # Load model configuration
    if config_path is None:
        # Use default test config
        config_path = 'options/test/dimo_test.yml'
    
    # Parse options
    opt = parse_options(config_path, is_train=False)
    opt['path']['pretrain_network_g'] = model_path
    
    # Create model
    model = build_model(opt)
    
    # Load input images
    # Assuming input_path contains polarization images in specific format
    # You may need to adapt this based on your data format
    input_files = {
        'lq_rgb': osp.join(input_path, 'rgb.png'),
        'lq_img0': osp.join(input_path, 'img0.png'),
        'lq_img45': osp.join(input_path, 'img45.png'),
        'lq_img90': osp.join(input_path, 'img90.png'),
        'lq_img135': osp.join(input_path, 'img135.png'),
        'lq_aolp': osp.join(input_path, 'aolp.png'),
        'lq_dolp': osp.join(input_path, 'dolp.png'),
        'lq_Ip': osp.join(input_path, 'Ip.png'),
        'lq_Inp': osp.join(input_path, 'Inp.png'),
    }
    
    # Check if all required files exist
    for key, file_path in input_files.items():
        if not osp.exists(file_path):
            raise FileNotFoundError(f"Required input file not found: {file_path}")
    
    # Load and preprocess images
    data = {}
    for key, file_path in input_files.items():
        img = cv2.imread(file_path, cv2.IMREAD_UNCHANGED)
        if img is None:
            raise ValueError(f"Failed to load image: {file_path}")
        
        # Convert to tensor and normalize
        if img.ndim == 2:
            img = img[..., None]
        img = img.astype(np.float32) / 255.0
        img = torch.from_numpy(np.transpose(img, (2, 0, 1))).float().unsqueeze(0)
        data[key] = img
    
    # Run inference
    model.feed_data(data)
    model.test()
    
    # Get result
    visuals = model.get_current_visuals()
    result_img = visuals['result']
    
    # Convert back to numpy and save
    result_img = result_img.squeeze(0).cpu().numpy()
    result_img = np.transpose(result_img, (1, 2, 0))
    result_img = np.clip(result_img * 255.0, 0, 255).astype(np.uint8)
    
    # Save result
    os.makedirs(osp.dirname(output_path), exist_ok=True)
    cv2.imwrite(output_path, result_img)
    print(f"Enhanced image saved to: {output_path}")


def benchmark_speed(model_path, config_path=None, num_runs=100):
    """Benchmark the inference speed of DiMO model.
    
    Args:
        model_path (str): Path to the trained DiMO model
        config_path (str): Path to the config file (optional)
        num_runs (int): Number of runs for benchmarking
    """
    import time
    
    # Load model configuration
    if config_path is None:
        config_path = 'options/test/dimo_test.yml'
    
    opt = parse_options(config_path, is_train=False)
    opt['path']['pretrain_network_g'] = model_path
    
    # Create model
    model = build_model(opt)
    model.net_g.eval()
    model.net_le.eval()
    model.net_student.eval()
    
    # Create dummy input
    device = next(model.net_g.parameters()).device
    dummy_data = {
        'lq_rgb': torch.randn(1, 3, 256, 256).to(device),
        'lq_img0': torch.randn(1, 1, 256, 256).to(device),
        'lq_img45': torch.randn(1, 1, 256, 256).to(device),
        'lq_img90': torch.randn(1, 1, 256, 256).to(device),
        'lq_img135': torch.randn(1, 1, 256, 256).to(device),
        'lq_aolp': torch.randn(1, 1, 256, 256).to(device),
        'lq_dolp': torch.randn(1, 1, 256, 256).to(device),
        'lq_Ip': torch.randn(1, 1, 256, 256).to(device),
        'lq_Inp': torch.randn(1, 1, 256, 256).to(device),
    }
    
    # Warm up
    for _ in range(10):
        model.feed_data(dummy_data)
        model.test()
    
    # Benchmark
    torch.cuda.synchronize()
    start_time = time.time()
    
    for _ in range(num_runs):
        model.feed_data(dummy_data)
        model.test()
    
    torch.cuda.synchronize()
    end_time = time.time()
    
    avg_time = (end_time - start_time) / num_runs
    fps = 1.0 / avg_time
    
    print(f"Average inference time: {avg_time:.4f} seconds")
    print(f"Average FPS: {fps:.2f}")
    print(f"Total time for {num_runs} runs: {end_time - start_time:.4f} seconds")


if __name__ == '__main__':
    parser = argparse.ArgumentParser()
    parser.add_argument('--opt', type=str, default='options/test/dimo_test.yml', help='Path to option YAML file.')
    parser.add_argument('--mode', type=str, default='test', choices=['test', 'single', 'benchmark'], 
                       help='Test mode: test (full dataset), single (single image), benchmark (speed test)')
    parser.add_argument('--model_path', type=str, help='Path to the trained model')
    parser.add_argument('--input_path', type=str, help='Path to input image directory (for single mode)')
    parser.add_argument('--output_path', type=str, help='Path to save output image (for single mode)')
    parser.add_argument('--num_runs', type=int, default=100, help='Number of runs for benchmarking')
    
    args = parser.parse_args()
    
    if args.mode == 'test':
        main()
    elif args.mode == 'single':
        if not args.model_path or not args.input_path or not args.output_path:
            raise ValueError("For single mode, --model_path, --input_path, and --output_path are required")
        test_single_image(args.model_path, args.input_path, args.output_path, args.opt)
    elif args.mode == 'benchmark':
        if not args.model_path:
            raise ValueError("For benchmark mode, --model_path is required")
        benchmark_speed(args.model_path, args.opt, args.num_runs)

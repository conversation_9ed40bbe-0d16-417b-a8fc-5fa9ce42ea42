#!/usr/bin/env python3
"""
DiMO Demo Script
A simple demonstration of DiMO (Distilling Masked Diffusion Models into One-step Generator)
for polarization image enhancement.
"""

import argparse
import cv2
import numpy as np
import os
import torch
import yaml
from os import path as osp

from polarfree.models import build_model


class DiMOInference:
    """DiMO inference class for easy usage."""
    
    def __init__(self, model_path, config_path=None, device='cuda'):
        """
        Initialize DiMO inference.
        
        Args:
            model_path (str): Path to the trained DiMO model
            config_path (str): Path to the config file (optional)
            device (str): Device to run inference on
        """
        self.device = device
        self.model_path = model_path
        
        # Load configuration
        if config_path is None:
            config_path = 'options/test/dimo_test.yml'
        
        with open(config_path, 'r') as f:
            self.opt = yaml.safe_load(f)
        
        # Update model path
        self.opt['path']['pretrain_network_g'] = model_path
        self.opt['num_gpu'] = 1 if device == 'cuda' else 0
        self.opt['dist'] = False
        self.opt['is_train'] = False
        
        # Build model
        self.model = build_model(self.opt)
        self.model.net_g.eval()
        self.model.net_le.eval()
        self.model.net_student.eval()
        
        print(f"DiMO model loaded from: {model_path}")
        print(f"Running on device: {device}")
    
    def load_polarization_images(self, image_dir):
        """
        Load polarization images from directory.
        
        Args:
            image_dir (str): Directory containing polarization images
            
        Returns:
            dict: Dictionary of loaded images as tensors
        """
        required_files = {
            'lq_rgb': 'rgb.png',
            'lq_img0': 'img0.png',
            'lq_img45': 'img45.png',
            'lq_img90': 'img90.png',
            'lq_img135': 'img135.png',
            'lq_aolp': 'aolp.png',
            'lq_dolp': 'dolp.png',
            'lq_Ip': 'Ip.png',
            'lq_Inp': 'Inp.png',
        }
        
        data = {}
        for key, filename in required_files.items():
            file_path = osp.join(image_dir, filename)
            
            if not osp.exists(file_path):
                raise FileNotFoundError(f"Required file not found: {file_path}")
            
            # Load image
            img = cv2.imread(file_path, cv2.IMREAD_UNCHANGED)
            if img is None:
                raise ValueError(f"Failed to load image: {file_path}")
            
            # Handle different image formats
            if img.ndim == 2:
                img = img[..., None]
            elif img.ndim == 3 and img.shape[2] == 3:
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            # Normalize to [0, 1]
            img = img.astype(np.float32) / 255.0
            
            # Convert to tensor [C, H, W]
            img_tensor = torch.from_numpy(np.transpose(img, (2, 0, 1))).float()
            
            # Add batch dimension [1, C, H, W]
            data[key] = img_tensor.unsqueeze(0).to(self.device)
        
        return data
    
    def enhance_image(self, image_dir, output_path=None):
        """
        Enhance polarization images using DiMO.
        
        Args:
            image_dir (str): Directory containing input polarization images
            output_path (str): Path to save enhanced image (optional)
            
        Returns:
            np.ndarray: Enhanced image as numpy array
        """
        # Load input images
        data = self.load_polarization_images(image_dir)
        
        # Run inference
        with torch.no_grad():
            self.model.feed_data(data)
            self.model.test()
        
        # Get result
        visuals = self.model.get_current_visuals()
        result_tensor = visuals['result']
        
        # Convert to numpy array
        result_img = result_tensor.squeeze(0).cpu().numpy()
        result_img = np.transpose(result_img, (1, 2, 0))
        result_img = np.clip(result_img, 0, 1)
        
        # Convert to uint8
        result_img_uint8 = (result_img * 255).astype(np.uint8)
        
        # Save if output path is provided
        if output_path is not None:
            os.makedirs(osp.dirname(output_path), exist_ok=True)
            if result_img_uint8.shape[2] == 3:
                result_img_bgr = cv2.cvtColor(result_img_uint8, cv2.COLOR_RGB2BGR)
                cv2.imwrite(output_path, result_img_bgr)
            else:
                cv2.imwrite(output_path, result_img_uint8)
            print(f"Enhanced image saved to: {output_path}")
        
        return result_img
    
    def batch_enhance(self, input_dir, output_dir):
        """
        Enhance multiple sets of polarization images.
        
        Args:
            input_dir (str): Directory containing subdirectories of polarization images
            output_dir (str): Directory to save enhanced images
        """
        os.makedirs(output_dir, exist_ok=True)
        
        # Find all subdirectories
        subdirs = [d for d in os.listdir(input_dir) 
                  if osp.isdir(osp.join(input_dir, d))]
        
        print(f"Found {len(subdirs)} image sets to process")
        
        for i, subdir in enumerate(subdirs):
            print(f"Processing {i+1}/{len(subdirs)}: {subdir}")
            
            input_path = osp.join(input_dir, subdir)
            output_path = osp.join(output_dir, f"{subdir}_enhanced.png")
            
            try:
                self.enhance_image(input_path, output_path)
            except Exception as e:
                print(f"Error processing {subdir}: {str(e)}")
                continue
        
        print("Batch processing completed!")


def main():
    parser = argparse.ArgumentParser(description='DiMO Inference Demo')
    parser.add_argument('--model_path', type=str, required=True,
                       help='Path to the trained DiMO model')
    parser.add_argument('--config_path', type=str, default=None,
                       help='Path to the config file')
    parser.add_argument('--input_dir', type=str, required=True,
                       help='Directory containing input polarization images')
    parser.add_argument('--output_path', type=str, default=None,
                       help='Path to save enhanced image')
    parser.add_argument('--batch_mode', action='store_true',
                       help='Enable batch processing mode')
    parser.add_argument('--output_dir', type=str, default='./enhanced_results',
                       help='Output directory for batch mode')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cuda', 'cpu'], help='Device to run inference on')
    
    args = parser.parse_args()
    
    # Check if model exists
    if not osp.exists(args.model_path):
        raise FileNotFoundError(f"Model file not found: {args.model_path}")
    
    # Check if input directory exists
    if not osp.exists(args.input_dir):
        raise FileNotFoundError(f"Input directory not found: {args.input_dir}")
    
    # Initialize DiMO inference
    dimo = DiMOInference(args.model_path, args.config_path, args.device)
    
    if args.batch_mode:
        # Batch processing
        dimo.batch_enhance(args.input_dir, args.output_dir)
    else:
        # Single image processing
        if args.output_path is None:
            args.output_path = './enhanced_result.png'
        
        enhanced_img = dimo.enhance_image(args.input_dir, args.output_path)
        print(f"Enhancement completed! Output shape: {enhanced_img.shape}")


if __name__ == '__main__':
    main()

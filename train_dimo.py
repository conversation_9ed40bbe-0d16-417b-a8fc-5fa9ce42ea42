#!/usr/bin/env python3
"""
DiMO Training Script
Train a DiMO (Distilling Masked Diffusion Models into One-step Generator) model
for polarization image enhancement.
"""

import argparse
import datetime
import logging
import math
import os
import random
import time
import torch
from os import path as osp

from basicsr.data import build_dataloader, build_dataset
from basicsr.data.data_sampler import EnlargedSampler
from basicsr.data.prefetch_dataloader import CPUPrefetcher, CUDAPrefetcher
from basicsr.models import build_model as build_basicsr_model
from basicsr.utils import (MessageLogger, check_resume, get_env_info, get_root_logger,
                          get_time_str, init_tb_logger, init_wandb_logger, make_exp_dirs,
                          mkdir_and_rename, scandir, set_random_seed)
from basicsr.utils.dist_util import get_dist_info, init_dist
from basicsr.utils.options import copy_opt_file, dict2str
from polarfree.utils.options import parse_options

from polarfree.models import build_model
from polarfree.data import build_dataloader as build_polarfree_dataloader


def init_tb_loggers(opt):
    """Initialize tensorboard loggers."""
    # Initialize tb_logger
    tb_logger = None
    if opt['logger'].get('use_tb_logger') and 'debug' not in opt['name']:
        tb_logger = init_tb_logger(log_dir=osp.join(opt['root_path'], 'tb_logger', opt['name']))
    return tb_logger


def create_train_val_dataloader(opt, logger):
    """Create training and validation dataloaders."""
    # Create train and val dataloaders
    train_loader, train_sampler = None, None
    for phase, dataset_opt in opt['datasets'].items():
        if phase == 'train':
            dataset_enlarge_ratio = dataset_opt.get('dataset_enlarge_ratio', 1)
            train_set = build_dataset(dataset_opt)
            train_sampler = EnlargedSampler(train_set, opt['world_size'], opt['rank'], dataset_enlarge_ratio)
            train_loader = build_dataloader(
                train_set,
                dataset_opt,
                num_gpu=opt['num_gpu'],
                dist=opt['dist'],
                sampler=train_sampler,
                seed=opt['manual_seed'])

            num_iter_per_epoch = math.ceil(len(train_set) * dataset_enlarge_ratio / (dataset_opt['batch_size_per_gpu'] * opt['world_size']))
            total_iters = int(opt['train']['total_iter'])
            total_epochs = math.ceil(total_iters / (num_iter_per_epoch))
            logger.info('Training statistics:'
                       f'\n\tNumber of train images: {len(train_set)}'
                       f'\n\tDataset enlarge ratio: {dataset_enlarge_ratio}'
                       f'\n\tBatch size per gpu: {dataset_opt["batch_size_per_gpu"]}'
                       f'\n\tWorld size (gpu number): {opt["world_size"]}'
                       f'\n\tRequire iter number per epoch: {num_iter_per_epoch}'
                       f'\n\tTotal epochs: {total_epochs}; iters: {total_iters}.')
            break
    
    # Create val dataloaders
    val_loaders = []
    for phase, dataset_opt in opt['datasets'].items():
        if phase.startswith('val'):
            val_set = build_dataset(dataset_opt)
            val_loader = build_dataloader(
                val_set, dataset_opt, num_gpu=opt['num_gpu'], dist=opt['dist'], sampler=None, seed=opt['manual_seed'])
            logger.info(f'Number of val images/folders in {dataset_opt["name"]}: {len(val_set)}')
            val_loaders.append(val_loader)

    return train_loader, train_sampler, val_loaders, total_epochs, total_iters


def main():
    # Parse options, set distributed setting, set random seed
    root_path = osp.abspath(osp.join(__file__, osp.pardir))
    opt, args = parse_options(root_path, is_train=True)
    opt['root_path'] = root_path
    
    # Distributed training settings
    if opt['dist']:
        init_dist('nccl')
    rank, world_size = get_dist_info()
    opt['rank'] = rank
    opt['world_size'] = world_size

    # Load resume states if necessary
    if opt['path'].get('resume_state'):
        device_id = torch.cuda.current_device()
        resume_state = torch.load(
            opt['path']['resume_state'], map_location=lambda storage, loc: storage.cuda(device_id))
    else:
        resume_state = None

    # Mkdir for experiments and logger
    if resume_state is None:
        # Create directories manually to avoid None path issues
        directories = [
            opt['path']['experiments_root'],
            opt['path']['models'],
            opt['path']['training_states'],
            opt['path']['log'],
            opt['path']['visualization']
        ]

        for directory in directories:
            if directory is not None:
                os.makedirs(directory, exist_ok=True)

        if opt['logger'].get('use_tb_logger') and 'debug' not in opt['name'] and rank == 0:
            mkdir_and_rename(osp.join(opt['root_path'], 'tb_logger', opt['name']))

    # Copy the yml file to the experiment root
    copy_opt_file(args.opt, opt['path']['experiments_root'])

    # WARNING: should not use get_root_logger in the above codes, including the called functions
    # Otherwise the logger will not be properly initialized
    log_file = osp.join(opt['path']['log'], f"train_{opt['name']}_{get_time_str()}.log")
    logger = get_root_logger(logger_name='basicsr', log_level=logging.INFO, log_file=log_file)
    logger.info(get_env_info())
    logger.info(dict2str(opt))

    # Initialize tb and wandb loggers
    tb_logger = init_tb_loggers(opt)
    
    # Initialize wandb logger before resuming training
    if (opt['logger'].get('wandb') is not None) and (opt['logger']['wandb'].get('project') is not None) and (opt['logger']['wandb'].get('project') != 'null') and ('debug' not in opt['name']):
        assert opt['logger'].get('use_tb_logger') is True, ('should turn on tensorboard when using wandb')
        init_wandb_logger(opt)
    else:
        opt['logger']['wandb'] = None

    # Set random seed
    seed = opt['manual_seed']
    if seed is None:
        seed = random.randint(1, 10000)
        opt['manual_seed'] = seed
    set_random_seed(seed + rank)

    torch.backends.cudnn.benchmark = True

    # Create train and validation dataloaders
    result = create_train_val_dataloader(opt, logger)
    train_loader, train_sampler, val_loaders, total_epochs, total_iters = result

    # Create model
    if resume_state:  # resume training
        check_resume(opt, resume_state['iter'])
        model = build_model(opt)
        model.resume_training(resume_state)  # handle optimizers and schedulers
        logger.info(f"Resuming training from epoch: {resume_state['epoch']}, iter: {resume_state['iter']}.")
        start_epoch = resume_state['epoch']
        current_iter = resume_state['iter']
    else:
        model = build_model(opt)
        start_epoch = 0
        current_iter = 0

    # Create message logger (formatted outputs)
    msg_logger = MessageLogger(opt, current_iter, tb_logger)

    # Dataloader prefetcher
    prefetch_mode = opt['datasets']['train'].get('prefetch_mode')
    if prefetch_mode is None or prefetch_mode == 'cpu':
        prefetcher = CPUPrefetcher(train_loader)
    elif prefetch_mode == 'cuda':
        prefetcher = CUDAPrefetcher(train_loader, opt)
        logger.info(f'Use {prefetch_mode} prefetch dataloader')
        if opt['datasets']['train'].get('pin_memory') is not True:
            raise ValueError('Please set pin_memory=True for CUDAPrefetcher.')
    else:
        raise ValueError(f'Wrong prefetch_mode {prefetch_mode}. Supported ones are: None, \'cuda\', \'cpu\'.')

    # Training loop
    logger.info(f'Start training from epoch: {start_epoch}, iter: {current_iter}')
    data_timer, iter_timer = time.time(), time.time()
    start_time = time.time()

    for epoch in range(start_epoch, total_epochs + 1):
        train_sampler.set_epoch(epoch)
        prefetcher.reset()
        train_data = prefetcher.next()

        while train_data is not None:
            data_time = time.time() - data_timer
            current_iter += 1
            if current_iter > total_iters:
                break

            # Update learning rate
            model.update_learning_rate(current_iter, warmup_iter=opt['train'].get('warmup_iter', -1))

            # Training
            model.feed_data(train_data)
            model.optimize_parameters(current_iter)
            iter_time = time.time() - iter_timer
            
            # Log
            if current_iter % opt['logger']['print_freq'] == 0:
                log_vars = {'epoch': epoch, 'iter': current_iter}
                log_vars.update({'lrs': model.get_current_learning_rate()})
                log_vars.update({'time': iter_time, 'data_time': data_time})
                log_vars.update(model.get_current_log())
                msg_logger(log_vars)

            # Save models and training states
            if current_iter % opt['logger']['save_checkpoint_freq'] == 0:
                logger.info('Saving models and training states.')
                model.save(epoch, current_iter)

            # Validation
            if opt.get('val') is not None and (current_iter % opt['val']['val_freq'] == 0):
                if len(val_loaders) > 1:
                    logger.warning('Multiple validation datasets are *only* supported by SRModel.')
                for val_loader in val_loaders:
                    model.validation(val_loader, current_iter, tb_logger, opt['val']['save_img'])

            data_timer = time.time()
            iter_timer = time.time()
            train_data = prefetcher.next()
        # End of iter

    # End of epoch
    consumed_time = str(datetime.timedelta(seconds=int(time.time() - start_time)))
    logger.info(f'End of training. Time consumed: {consumed_time}')
    logger.info('Save the latest model.')
    model.save(epoch=-1, current_iter=-1)  # -1 stands for the latest

    if tb_logger:
        tb_logger.close()


if __name__ == '__main__':
    main()

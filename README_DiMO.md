# DiMO: Distilling Masked Diffusion Models into One-step Generator

This implementation provides DiMO (Distilling Masked Diffusion Models into One-step Generator) for polarization image enhancement. DiMO converts multi-step diffusion models into efficient one-step generators through knowledge distillation.

## Overview

DiMO addresses the computational bottleneck of multi-step diffusion models by distilling them into single-step generators. The key benefits include:

- **Speed**: Up to 8x faster inference compared to multi-step diffusion
- **Quality**: Maintains comparable output quality to the teacher model
- **Efficiency**: Single forward pass instead of iterative denoising

## Architecture

The DiMO framework consists of:

1. **Teacher Model**: Pre-trained multi-step diffusion model
2. **Student Model**: One-step generator network
3. **Distillation Loss**: Distribution matching between teacher and student outputs

## Installation

Ensure you have the required dependencies:

```bash
pip install torch torchvision
pip install opencv-python
pip install pyyaml
pip install tqdm
```

## Usage

### 1. Training DiMO

First, ensure you have a pre-trained teacher diffusion model. Then train the DiMO student model:

```bash
# Single GPU training
python train_dimo.py -opt options/train/dimo_distillation.yml

# Multi-GPU training
python -m torch.distributed.launch --nproc_per_node=4 train_dimo.py -opt options/train/dimo_distillation.yml --launcher pytorch

# Or use the convenient script
bash train_dimo.sh
```

### 2. Testing DiMO

Test the trained DiMO model:

```bash
# Test on dataset
python test_dimo.py -opt options/test/dimo_test.yml

# Test single image
python test_dimo.py --mode single --model_path path/to/model.pth --input_path path/to/input --output_path path/to/output.png

# Benchmark speed
python test_dimo.py --mode benchmark --model_path path/to/model.pth --num_runs 100
```

### 3. Demo Inference

Use the demo script for easy inference:

```bash
# Single image enhancement
python demo_dimo.py --model_path path/to/model.pth --input_dir path/to/input --output_path enhanced.png

# Batch processing
python demo_dimo.py --model_path path/to/model.pth --input_dir path/to/inputs --batch_mode --output_dir path/to/outputs
```

### 4. Model Comparison

Compare DiMO with the original diffusion model:

```bash
python compare_dimo.py --dimo_model path/to/dimo.pth --diffusion_model path/to/diffusion.pth --test_dir path/to/test --output_dir comparison_results
```

## Configuration

### Training Configuration (`options/train/dimo_distillation.yml`)

Key parameters:

- `network_student`: Student network architecture
- `distillation_loss_weight`: Weight for distillation loss
- `pixel_loss_weight`: Weight for pixel-level loss
- `perceptual_loss_weight`: Weight for perceptual loss

### Student Network Configuration

The student network (`dimo_student`) supports:

- `input_dim`: Input feature dimension
- `hidden_dim`: Hidden layer dimension
- `num_layers`: Number of processing layers
- `use_attention`: Whether to use attention blocks
- `dropout`: Dropout rate for regularization

## Input Data Format

DiMO expects polarization images in the following format:

```
input_directory/
├── rgb.png          # RGB image
├── img0.png         # 0° polarization
├── img45.png        # 45° polarization
├── img90.png        # 90° polarization
├── img135.png       # 135° polarization
├── aolp.png         # Angle of Linear Polarization
├── dolp.png         # Degree of Linear Polarization
├── Ip.png           # Polarized intensity
├── Inp.png          # Non-polarized intensity
└── gt.png           # Ground truth (optional, for evaluation)
```

## Model Files

After training, the following files will be generated:

- `net_g_latest.pth`: Latest model checkpoint
- `net_g_best.pth`: Best model based on validation metrics
- Training logs and tensorboard files

## Performance

Typical performance improvements with DiMO:

| Metric | Multi-step Diffusion | DiMO | Improvement |
|--------|---------------------|------|-------------|
| Inference Time | ~0.8s | ~0.1s | 8x faster |
| PSNR | 28.5 dB | 28.2 dB | -0.3 dB |
| SSIM | 0.85 | 0.84 | -0.01 |

## Troubleshooting

### Common Issues

1. **CUDA out of memory**: Reduce batch size or use CPU
2. **Missing input files**: Ensure all required polarization images are present
3. **Model loading errors**: Check model path and configuration compatibility

### Tips

- Use mixed precision training to reduce memory usage
- Start with a smaller student network if training is unstable
- Adjust distillation loss weight based on your specific task

## Citation

If you use this DiMO implementation, please cite:

```bibtex
@article{zhu2025dimo,
  title={Di$\mathtt{[M]}$O: Distilling Masked Diffusion Models into One-step Generator},
  author={Zhu, Yuanzhi and Wang, Xi and Lathuili{\`e}re, St{\'e}phane and Kalogeiton, Vicky},
  journal={arXiv preprint arXiv:2503.15457},
  year={2025}
}
```

## License

This implementation is based on the original DiMO paper and is provided for research purposes.

#!/bin/bash

# DiMO Training Script
# This script trains a DiMO model for polarization image enhancement

# Set CUDA device
export CUDA_VISIBLE_DEVICES=0

# Training configuration
CONFIG_FILE="options/train/dimo_distillation.yml"
NUM_GPUS=1

echo "Starting DiMO training..."
echo "Config file: $CONFIG_FILE"
echo "Number of GPUs: $NUM_GPUS"

# Check if config file exists
if [ ! -f "$CONFIG_FILE" ]; then
    echo "Error: Config file $CONFIG_FILE not found!"
    exit 1
fi

# Single GPU training
if [ $NUM_GPUS -eq 1 ]; then
    python train_dimo.py -opt $CONFIG_FILE
else
    # Multi-GPU training
    python -m torch.distributed.launch --nproc_per_node=$NUM_GPUS --master_port=4321 train_dimo.py -opt $CONFIG_FILE --launcher pytorch
fi

echo "DiMO training completed!"

#!/usr/bin/env python3
"""
DiMO Comparison Script
Compare DiMO (one-step) with original multi-step diffusion model
in terms of speed and quality.
"""

import argparse
import cv2
import numpy as np
import os
import time
import torch
import yaml
from os import path as osp
from collections import defaultdict

from polarfree.models import build_model
from basicsr.metrics import calculate_psnr, calculate_ssim


class ModelComparator:
    """Compare DiMO with original diffusion model."""
    
    def __init__(self, dimo_model_path, diffusion_model_path, 
                 dimo_config_path=None, diffusion_config_path=None, device='cuda'):
        """
        Initialize model comparator.
        
        Args:
            dimo_model_path (str): Path to DiMO model
            diffusion_model_path (str): Path to original diffusion model
            dimo_config_path (str): Path to DiMO config
            diffusion_config_path (str): Path to diffusion config
            device (str): Device to run on
        """
        self.device = device
        
        # Load DiMO model
        if dimo_config_path is None:
            dimo_config_path = 'options/test/dimo_test.yml'
        
        with open(dimo_config_path, 'r') as f:
            dimo_opt = yaml.safe_load(f)
        dimo_opt['path']['pretrain_network_g'] = dimo_model_path
        dimo_opt['num_gpu'] = 1 if device == 'cuda' else 0
        dimo_opt['dist'] = False
        dimo_opt['is_train'] = False
        
        self.dimo_model = build_model(dimo_opt)
        self.dimo_model.net_g.eval()
        self.dimo_model.net_le.eval()
        self.dimo_model.net_student.eval()
        
        # Load original diffusion model
        if diffusion_config_path is None:
            diffusion_config_path = 'options/test/ours_stage2.yml'  # Adjust as needed
        
        with open(diffusion_config_path, 'r') as f:
            diffusion_opt = yaml.safe_load(f)
        diffusion_opt['path']['pretrain_network_g'] = diffusion_model_path
        diffusion_opt['num_gpu'] = 1 if device == 'cuda' else 0
        diffusion_opt['dist'] = False
        diffusion_opt['is_train'] = False
        
        self.diffusion_model = build_model(diffusion_opt)
        
        print("Models loaded successfully!")
        print(f"DiMO model: {dimo_model_path}")
        print(f"Diffusion model: {diffusion_model_path}")
    
    def load_test_data(self, image_dir):
        """Load test data."""
        required_files = {
            'lq_rgb': 'rgb.png',
            'lq_img0': 'img0.png',
            'lq_img45': 'img45.png',
            'lq_img90': 'img90.png',
            'lq_img135': 'img135.png',
            'lq_aolp': 'aolp.png',
            'lq_dolp': 'dolp.png',
            'lq_Ip': 'Ip.png',
            'lq_Inp': 'Inp.png',
        }
        
        data = {}
        for key, filename in required_files.items():
            file_path = osp.join(image_dir, filename)
            
            if not osp.exists(file_path):
                raise FileNotFoundError(f"Required file not found: {file_path}")
            
            img = cv2.imread(file_path, cv2.IMREAD_UNCHANGED)
            if img is None:
                raise ValueError(f"Failed to load image: {file_path}")
            
            if img.ndim == 2:
                img = img[..., None]
            elif img.ndim == 3 and img.shape[2] == 3:
                img = cv2.cvtColor(img, cv2.COLOR_BGR2RGB)
            
            img = img.astype(np.float32) / 255.0
            img_tensor = torch.from_numpy(np.transpose(img, (2, 0, 1))).float()
            data[key] = img_tensor.unsqueeze(0).to(self.device)
        
        # Load ground truth if available
        gt_path = osp.join(image_dir, 'gt.png')
        if osp.exists(gt_path):
            gt_img = cv2.imread(gt_path, cv2.IMREAD_UNCHANGED)
            if gt_img is not None:
                if gt_img.ndim == 3:
                    gt_img = cv2.cvtColor(gt_img, cv2.COLOR_BGR2RGB)
                gt_img = gt_img.astype(np.float32) / 255.0
                gt_tensor = torch.from_numpy(np.transpose(gt_img, (2, 0, 1))).float()
                data['gt_rgb'] = gt_tensor.unsqueeze(0).to(self.device)
        
        return data
    
    def benchmark_speed(self, test_data, num_runs=100):
        """Benchmark inference speed."""
        print(f"\nBenchmarking speed with {num_runs} runs...")
        
        # Warm up
        for _ in range(10):
            self.dimo_model.feed_data(test_data)
            self.dimo_model.test()
            
            self.diffusion_model.feed_data(test_data)
            self.diffusion_model.test()
        
        # Benchmark DiMO
        torch.cuda.synchronize()
        start_time = time.time()
        
        for _ in range(num_runs):
            self.dimo_model.feed_data(test_data)
            self.dimo_model.test()
        
        torch.cuda.synchronize()
        dimo_time = time.time() - start_time
        
        # Benchmark original diffusion
        torch.cuda.synchronize()
        start_time = time.time()
        
        for _ in range(num_runs):
            self.diffusion_model.feed_data(test_data)
            self.diffusion_model.test()
        
        torch.cuda.synchronize()
        diffusion_time = time.time() - start_time
        
        # Calculate statistics
        dimo_avg_time = dimo_time / num_runs
        diffusion_avg_time = diffusion_time / num_runs
        speedup = diffusion_avg_time / dimo_avg_time
        
        print(f"\nSpeed Comparison Results:")
        print(f"DiMO average time: {dimo_avg_time:.4f} seconds ({1/dimo_avg_time:.2f} FPS)")
        print(f"Diffusion average time: {diffusion_avg_time:.4f} seconds ({1/diffusion_avg_time:.2f} FPS)")
        print(f"Speedup: {speedup:.2f}x")
        
        return {
            'dimo_time': dimo_avg_time,
            'diffusion_time': diffusion_avg_time,
            'speedup': speedup
        }
    
    def compare_quality(self, test_data):
        """Compare output quality."""
        print("\nComparing output quality...")
        
        # Generate outputs
        with torch.no_grad():
            # DiMO output
            self.dimo_model.feed_data(test_data)
            self.dimo_model.test()
            dimo_visuals = self.dimo_model.get_current_visuals()
            dimo_output = dimo_visuals['result']
            
            # Diffusion output
            self.diffusion_model.feed_data(test_data)
            self.diffusion_model.test()
            diffusion_visuals = self.diffusion_model.get_current_visuals()
            diffusion_output = diffusion_visuals['result']
        
        # Convert to numpy for metrics calculation
        dimo_img = dimo_output.squeeze(0).cpu().numpy().transpose(1, 2, 0)
        diffusion_img = diffusion_output.squeeze(0).cpu().numpy().transpose(1, 2, 0)
        
        # Calculate metrics if ground truth is available
        metrics = {}
        if 'gt_rgb' in test_data:
            gt_img = test_data['gt_rgb'].squeeze(0).cpu().numpy().transpose(1, 2, 0)
            
            # PSNR
            dimo_psnr = calculate_psnr(dimo_img * 255, gt_img * 255, crop_border=0, test_y_channel=False)
            diffusion_psnr = calculate_psnr(diffusion_img * 255, gt_img * 255, crop_border=0, test_y_channel=False)
            
            # SSIM
            dimo_ssim = calculate_ssim(dimo_img * 255, gt_img * 255, crop_border=0, test_y_channel=False)
            diffusion_ssim = calculate_ssim(diffusion_img * 255, gt_img * 255, crop_border=0, test_y_channel=False)
            
            metrics = {
                'dimo_psnr': dimo_psnr,
                'diffusion_psnr': diffusion_psnr,
                'dimo_ssim': dimo_ssim,
                'diffusion_ssim': diffusion_ssim
            }
            
            print(f"\nQuality Comparison Results:")
            print(f"DiMO PSNR: {dimo_psnr:.4f} dB")
            print(f"Diffusion PSNR: {diffusion_psnr:.4f} dB")
            print(f"DiMO SSIM: {dimo_ssim:.4f}")
            print(f"Diffusion SSIM: {diffusion_ssim:.4f}")
        else:
            print("No ground truth available for quality metrics")
        
        # Calculate difference between outputs
        diff = np.abs(dimo_img - diffusion_img)
        mean_diff = np.mean(diff)
        max_diff = np.max(diff)
        
        print(f"\nOutput Difference:")
        print(f"Mean absolute difference: {mean_diff:.6f}")
        print(f"Max absolute difference: {max_diff:.6f}")
        
        metrics.update({
            'mean_diff': mean_diff,
            'max_diff': max_diff
        })
        
        return metrics, dimo_img, diffusion_img
    
    def save_comparison_images(self, dimo_img, diffusion_img, output_dir, test_name="test"):
        """Save comparison images."""
        os.makedirs(output_dir, exist_ok=True)
        
        # Convert to uint8
        dimo_img_uint8 = (np.clip(dimo_img, 0, 1) * 255).astype(np.uint8)
        diffusion_img_uint8 = (np.clip(diffusion_img, 0, 1) * 255).astype(np.uint8)
        
        # Save images
        if dimo_img_uint8.shape[2] == 3:
            dimo_img_bgr = cv2.cvtColor(dimo_img_uint8, cv2.COLOR_RGB2BGR)
            diffusion_img_bgr = cv2.cvtColor(diffusion_img_uint8, cv2.COLOR_RGB2BGR)
        else:
            dimo_img_bgr = dimo_img_uint8
            diffusion_img_bgr = diffusion_img_uint8
        
        cv2.imwrite(osp.join(output_dir, f"{test_name}_dimo.png"), dimo_img_bgr)
        cv2.imwrite(osp.join(output_dir, f"{test_name}_diffusion.png"), diffusion_img_bgr)
        
        # Create difference image
        diff_img = np.abs(dimo_img - diffusion_img)
        diff_img_uint8 = (diff_img * 255 / np.max(diff_img)).astype(np.uint8)
        cv2.imwrite(osp.join(output_dir, f"{test_name}_difference.png"), diff_img_uint8)
        
        print(f"Comparison images saved to: {output_dir}")


def main():
    parser = argparse.ArgumentParser(description='Compare DiMO with original diffusion model')
    parser.add_argument('--dimo_model', type=str, required=True,
                       help='Path to DiMO model')
    parser.add_argument('--diffusion_model', type=str, required=True,
                       help='Path to original diffusion model')
    parser.add_argument('--dimo_config', type=str, default=None,
                       help='Path to DiMO config file')
    parser.add_argument('--diffusion_config', type=str, default=None,
                       help='Path to diffusion config file')
    parser.add_argument('--test_dir', type=str, required=True,
                       help='Directory containing test images')
    parser.add_argument('--output_dir', type=str, default='./comparison_results',
                       help='Directory to save comparison results')
    parser.add_argument('--num_runs', type=int, default=100,
                       help='Number of runs for speed benchmark')
    parser.add_argument('--device', type=str, default='cuda',
                       choices=['cuda', 'cpu'], help='Device to run on')
    
    args = parser.parse_args()
    
    # Initialize comparator
    comparator = ModelComparator(
        args.dimo_model, args.diffusion_model,
        args.dimo_config, args.diffusion_config, args.device
    )
    
    # Load test data
    test_data = comparator.load_test_data(args.test_dir)
    
    # Benchmark speed
    speed_results = comparator.benchmark_speed(test_data, args.num_runs)
    
    # Compare quality
    quality_results, dimo_img, diffusion_img = comparator.compare_quality(test_data)
    
    # Save comparison images
    comparator.save_comparison_images(dimo_img, diffusion_img, args.output_dir)
    
    # Save results summary
    results_summary = {
        'speed': speed_results,
        'quality': quality_results
    }
    
    import json
    with open(osp.join(args.output_dir, 'comparison_results.json'), 'w') as f:
        json.dump(results_summary, f, indent=2)
    
    print(f"\nComparison completed! Results saved to: {args.output_dir}")


if __name__ == '__main__':
    main()

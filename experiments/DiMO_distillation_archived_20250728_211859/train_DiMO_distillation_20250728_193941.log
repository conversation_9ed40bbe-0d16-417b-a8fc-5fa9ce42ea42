2025-07-28 19:39:41,707 INFO: 
  name: DiMO_distillation
  model_type: DiMO
  scale: 1
  num_gpu: 1
  manual_seed: 0
  datasets:[
    train:[
      name: TrainSet
      type: PairedImagePolarDataset
      dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
      dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
      filename_tmpl: {}
      io_backend:[
        type: disk
      ]
      test_scenes: []
      easy_data_ratio: 1
      hard_data_ratio: 3
      gt_size: 256
      use_hflip: True
      use_rot: True
      use_shuffle: True
      num_worker_per_gpu: 4
      batch_size_per_gpu: 2
      dataset_enlarge_ratio: 1
      prefetch_mode: None
      phase: train
      scale: 1
    ]
    val:[
      name: ValSet
      type: PairedImagePolarDataset
      dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/val
      dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/val
      io_backend:[
        type: disk
      ]
      test_scenes: ['00', '01', '02', '03', '04', '05', '06', '07']
      phase: val
      scale: 1
    ]
  ]
  network_g:[
    type: InvNet
    subnet_constructor: Resnet
    coupling_layers: 8
  ]
  network_le:[
    type: latent_encoder_gelu
    in_chans: 12
    embed_dim: 64
    block_num: 6
    group: 4
    stage: 1
    patch_expansion: 0.5
    channel_expansion: 4
  ]
  network_le_dm:[
    type: latent_encoder_gelu
    in_chans: 9
    embed_dim: 64
    block_num: 6
    group: 4
    stage: 2
    patch_expansion: 0.5
    channel_expansion: 4
  ]
  network_d:[
    type: denoising
    in_channel: 256
    out_channel: 256
    inner_channel: 512
    block_num: 4
    group: 4
    patch_expansion: 0.5
    channel_expansion: 2
  ]
  network_student:[
    type: dimo_student
    input_dim: 256
    prior_dim: 256
    output_dim: 256
    hidden_dim: 512
    num_layers: 6
    num_heads: 8
    group: 4
    patch_expansion: 0.5
    channel_expansion: 4
    use_attention: True
    dropout: 0.1
  ]
  diffusion_schedule:[
    apply_ldm: False
    schedule: linear
    timesteps: 8
    linear_start: 0.1
    linear_end: 0.99
  ]
  path:[
    pretrain_network_g: None
    pretrain_network_le: None
    pretrain_teacher: None
    strict_load_g: False
    strict_load_le: False
    resume_state: None
    experiments_root: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4/experiments/DiMO_distillation
    models: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4/experiments/DiMO_distillation/models
    training_states: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4/experiments/DiMO_distillation/training_states
    log: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4/experiments/DiMO_distillation
    visualization: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4/experiments/DiMO_distillation/visualization
  ]
  train:[
    ema_decay: 0.999
    optim_g:[
      type: Adam
      lr: 0.0002
      weight_decay: 0
      betas: [0.9, 0.99]
    ]
    scheduler:[
      type: MultiStepLR
      milestones: [50000, 100000, 200000, 300000]
      gamma: 0.5
    ]
    total_iter: 400000
    warmup_iter: -1
    distillation_loss_weight: 1.0
    distillation_loss:[
      type: DiMODistillationLoss
      loss_type: mse
      temperature: 1.0
      alpha: 0.7
      beta: 0.3
      use_feature_matching: True
    ]
    pixel_loss_weight: 0.1
    pixel_loss:[
      type: L1Loss
      loss_weight: 1.0
      reduction: mean
    ]
    perceptual_loss_weight: 0.1
    perceptual_loss:[
      type: PerceptualLoss
      layer_weights:[
        conv5_4: 1
      ]
      vgg_type: vgg19
      use_input_norm: True
      range_norm: False
      perceptual_weight: 1.0
      style_weight: 0
      criterion: l1
    ]
    style_loss_weight: 0
  ]
  val:[
    val_freq: 5000.0
    save_img: False
    metrics:[
      psnr:[
        type: calculate_psnr
        crop_border: 0
        test_y_channel: False
      ]
    ]
  ]
  logger:[
    print_freq: 100
    save_checkpoint_freq: 5000.0
    use_tb_logger: False
    wandb:[
      project: None
      resume_id: None
    ]
  ]
  dist_params:[
    backend: nccl
    port: 29500
  ]
  dist: False
  rank: 0
  world_size: 1
  auto_resume: False
  is_train: True
  root_path: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4

2025-07-28 19:39:41,709 INFO: Creating datasets...
2025-07-28 19:39:41,767 INFO: Dataset [PairedImagePolarDataset] - TrainSet is built.
2025-07-28 19:39:41,767 INFO: Number of train images: 9722
2025-07-28 19:39:41,767 INFO: Creating DiMO model...
2025-07-28 19:39:41,786 INFO: Network [InvNet] is created.
2025-07-28 19:39:41,982 INFO: Network: InvNet, with parameters: 1,556,544
2025-07-28 19:39:41,982 INFO: InvNet(
  (operations): ModuleList(
    (0-7): 8 x InvBlockExp_RNVP(
      (subnet_s1): ResBlock(
        (conv1): Conv2d(3, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (relu1): LeakyReLU(negative_slope=0.2, inplace=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (conv3): Conv2d(67, 16, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (subnet_t1): ResBlock(
        (conv1): Conv2d(3, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (relu1): LeakyReLU(negative_slope=0.2, inplace=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (conv3): Conv2d(67, 16, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (subnet_s2): ResBlock(
        (conv1): Conv2d(16, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (relu1): LeakyReLU(negative_slope=0.2, inplace=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (conv3): Conv2d(80, 3, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
      (subnet_t2): ResBlock(
        (conv1): Conv2d(16, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (relu1): LeakyReLU(negative_slope=0.2, inplace=True)
        (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
        (conv3): Conv2d(80, 3, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      )
    )
  )
  (linear_proj): Sequential(
    (0): ConvTranspose2d(256, 16, kernel_size=(1, 1), stride=(1, 1))
  )
  (down_pool): AdaptiveAvgPool2d(output_size=(4, 4))
  (linear_proj_inv): Sequential(
    (0): Conv2d(16, 256, kernel_size=(1, 1), stride=(1, 1))
  )
)
2025-07-28 19:39:41,988 INFO: Network [latent_encoder_gelu] is created.
2025-07-28 19:39:41,990 INFO: Network: latent_encoder_gelu, with parameters: 640,984
2025-07-28 19:39:41,990 INFO: latent_encoder_gelu(
  (pixel_unshuffle): PixelUnshuffle(downscale_factor=4)
  (conv1): Sequential(
    (0): Conv2d(192, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
    (1): GELU(approximate='none')
  )
  (blocks): ModuleList(
    (0-5): 6 x Sequential(
      (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      (1): GELU(approximate='none')
      (2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
    )
  )
  (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
  (pool): AdaptiveAvgPool2d(output_size=(4, 4))
  (mlp): MLP(
    (patch_mixer): Sequential(
      (0): Linear(in_features=16, out_features=8, bias=True)
      (1): GELU(approximate='none')
      (2): Linear(in_features=8, out_features=16, bias=True)
    )
    (channel_mixer): Sequential(
      (0): Linear(in_features=64, out_features=256, bias=True)
      (1): GELU(approximate='none')
      (2): Linear(in_features=256, out_features=64, bias=True)
    )
    (norm1): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
    (norm2): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
  )
  (end): Sequential(
    (0): Linear(in_features=64, out_features=256, bias=True)
    (1): GELU(approximate='none')
  )
)
2025-07-28 19:39:41,995 INFO: Network [latent_encoder_gelu] is created.
2025-07-28 19:39:41,996 INFO: Network: latent_encoder_gelu, with parameters: 613,336
2025-07-28 19:39:41,997 INFO: latent_encoder_gelu(
  (pixel_unshuffle): PixelUnshuffle(downscale_factor=4)
  (conv1): Sequential(
    (0): Conv2d(144, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
    (1): GELU(approximate='none')
  )
  (blocks): ModuleList(
    (0-5): 6 x Sequential(
      (0): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
      (1): GELU(approximate='none')
      (2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
    )
  )
  (conv2): Conv2d(64, 64, kernel_size=(3, 3), stride=(1, 1), padding=(1, 1))
  (pool): AdaptiveAvgPool2d(output_size=(4, 4))
  (mlp): MLP(
    (patch_mixer): Sequential(
      (0): Linear(in_features=16, out_features=8, bias=True)
      (1): GELU(approximate='none')
      (2): Linear(in_features=8, out_features=16, bias=True)
    )
    (channel_mixer): Sequential(
      (0): Linear(in_features=64, out_features=256, bias=True)
      (1): GELU(approximate='none')
      (2): Linear(in_features=256, out_features=64, bias=True)
    )
    (norm1): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
    (norm2): LayerNorm((64,), eps=1e-05, elementwise_affine=True)
  )
  (end): Sequential(
    (0): Linear(in_features=64, out_features=256, bias=True)
    (1): GELU(approximate='none')
  )
)
2025-07-28 19:39:42,012 INFO: Network [denoising] is created.
2025-07-28 19:39:42,015 INFO: Network: denoising, with parameters: 2,629,937
2025-07-28 19:39:42,015 INFO: denoising(
  (first_layer): Sequential(
    (0): Linear(in_features=768, out_features=512, bias=True)
    (1): LeakyReLU(negative_slope=0.2, inplace=True)
  )
  (blocks): ModuleList(
    (0-1): 2 x Sequential(
      (0): MLP(
        (patch_mixer): Sequential(
          (0): Linear(in_features=16, out_features=8, bias=True)
          (1): GELU(approximate='none')
          (2): Linear(in_features=8, out_features=16, bias=True)
        )
        (channel_mixer): Sequential(
          (0): Linear(in_features=512, out_features=1024, bias=True)
          (1): GELU(approximate='none')
          (2): Linear(in_features=1024, out_features=512, bias=True)
        )
        (norm1): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      )
      (1): GELU(approximate='none')
    )
  )
  (final_layer): Sequential(
    (0): Linear(in_features=512, out_features=256, bias=True)
    (1): GELU(approximate='none')
  )
)
2025-07-28 19:39:42,205 INFO: Network [dimo_student] is created.
2025-07-28 19:39:42,216 INFO: Network: dimo_student, with parameters: 16,224,072
2025-07-28 19:39:42,216 INFO: dimo_student(
  (input_proj): Sequential(
    (0): Linear(in_features=512, out_features=512, bias=True)
    (1): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
    (2): GELU(approximate='none')
    (3): Dropout(p=0.1, inplace=False)
  )
  (layers): ModuleList(
    (0): AttentionBlock(
      (attention): MultiheadAttention(
        (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
      )
      (norm1): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      (ffn): Sequential(
        (0): Linear(in_features=512, out_features=2048, bias=True)
        (1): GELU(approximate='none')
        (2): Dropout(p=0.1, inplace=False)
        (3): Linear(in_features=2048, out_features=512, bias=True)
        (4): Dropout(p=0.1, inplace=False)
      )
    )
    (1): Sequential(
      (0): MLP(
        (patch_mixer): Sequential(
          (0): Linear(in_features=16, out_features=8, bias=True)
          (1): GELU(approximate='none')
          (2): Linear(in_features=8, out_features=16, bias=True)
        )
        (channel_mixer): Sequential(
          (0): Linear(in_features=512, out_features=2048, bias=True)
          (1): GELU(approximate='none')
          (2): Linear(in_features=2048, out_features=512, bias=True)
        )
        (norm1): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      )
      (1): GELU(approximate='none')
      (2): Dropout(p=0.1, inplace=False)
    )
    (2): AttentionBlock(
      (attention): MultiheadAttention(
        (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
      )
      (norm1): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      (ffn): Sequential(
        (0): Linear(in_features=512, out_features=2048, bias=True)
        (1): GELU(approximate='none')
        (2): Dropout(p=0.1, inplace=False)
        (3): Linear(in_features=2048, out_features=512, bias=True)
        (4): Dropout(p=0.1, inplace=False)
      )
    )
    (3): Sequential(
      (0): MLP(
        (patch_mixer): Sequential(
          (0): Linear(in_features=16, out_features=8, bias=True)
          (1): GELU(approximate='none')
          (2): Linear(in_features=8, out_features=16, bias=True)
        )
        (channel_mixer): Sequential(
          (0): Linear(in_features=512, out_features=2048, bias=True)
          (1): GELU(approximate='none')
          (2): Linear(in_features=2048, out_features=512, bias=True)
        )
        (norm1): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      )
      (1): GELU(approximate='none')
      (2): Dropout(p=0.1, inplace=False)
    )
    (4): AttentionBlock(
      (attention): MultiheadAttention(
        (out_proj): NonDynamicallyQuantizableLinear(in_features=512, out_features=512, bias=True)
      )
      (norm1): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      (norm2): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      (ffn): Sequential(
        (0): Linear(in_features=512, out_features=2048, bias=True)
        (1): GELU(approximate='none')
        (2): Dropout(p=0.1, inplace=False)
        (3): Linear(in_features=2048, out_features=512, bias=True)
        (4): Dropout(p=0.1, inplace=False)
      )
    )
    (5): Sequential(
      (0): MLP(
        (patch_mixer): Sequential(
          (0): Linear(in_features=16, out_features=8, bias=True)
          (1): GELU(approximate='none')
          (2): Linear(in_features=8, out_features=16, bias=True)
        )
        (channel_mixer): Sequential(
          (0): Linear(in_features=512, out_features=2048, bias=True)
          (1): GELU(approximate='none')
          (2): Linear(in_features=2048, out_features=512, bias=True)
        )
        (norm1): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
        (norm2): LayerNorm((512,), eps=1e-05, elementwise_affine=True)
      )
      (1): GELU(approximate='none')
      (2): Dropout(p=0.1, inplace=False)
    )
  )
  (output_proj): Sequential(
    (0): Linear(in_features=512, out_features=256, bias=True)
    (1): GELU(approximate='none')
    (2): Dropout(p=0.1, inplace=False)
    (3): Linear(in_features=256, out_features=256, bias=True)
  )
  (residual_proj): Identity()
)
2025-07-28 19:39:42,216 INFO: No pretrained network_g provided, training from scratch
2025-07-28 19:39:42,217 INFO: No pretrained network_le provided, training from scratch
2025-07-28 19:39:42,217 WARNING: No pretrained teacher model provided. DiMO requires a trained teacher model for distillation.
2025-07-28 19:39:42,217 WARNING: The teacher model will be initialized randomly, which may lead to poor distillation results.

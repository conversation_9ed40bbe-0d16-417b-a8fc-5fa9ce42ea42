2025-07-28 19:18:04,971 INFO: 
                ____                _       _____  ____
               / __ ) ____ _ _____ (_)_____/ ___/ / __ \
              / __  |/ __ `// ___// // ___/\__ \ / /_/ /
             / /_/ // /_/ /(__  )/ // /__ ___/ // _, _/
            /_____/ \__,_//____//_/ \___//____//_/ |_|
     ______                   __   __                 __      __
    / ____/____   ____   ____/ /  / /   __  __ _____ / /__   / /
   / / __ / __ \ / __ \ / __  /  / /   / / / // ___// //_/  / /
  / /_/ // /_/ // /_/ // /_/ /  / /___/ /_/ // /__ / /<    /_/
  \____/ \____/ \____/ \____/  /_____/\____/ \___//_/|_|  (_)
    
Version Information: 
	BasicSR: 1.4.2
	PyTorch: 2.4.1+cu121
	TorchVision: 0.19.1+cu121
2025-07-28 19:18:04,971 INFO: 
  name: DiMO_distillation
  model_type: DiMO
  scale: 1
  num_gpu: 1
  manual_seed: 0
  datasets:[
    train:[
      name: TrainSet
      type: PairedImagePolarDataset
      dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
      dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
      filename_tmpl: {}
      io_backend:[
        type: disk
      ]
      test_scenes: []
      easy_data_ratio: 1
      hard_data_ratio: 3
      gt_size: 256
      use_hflip: True
      use_rot: True
      use_shuffle: True
      num_worker_per_gpu: 4
      batch_size_per_gpu: 2
      dataset_enlarge_ratio: 1
      prefetch_mode: None
      phase: train
      scale: 1
    ]
    val:[
      name: ValSet
      type: PairedImagePolarDataset
      dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/val
      dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/val
      io_backend:[
        type: disk
      ]
      phase: val
      scale: 1
    ]
  ]
  network_g:[
    type: InvNet
    embed_dim: 64
    group: 4
    block_num: 6
    patch_expansion: 0.5
    channel_expansion: 4
  ]
  network_le:[
    type: latent_encoder_gelu
    in_chans: 12
    embed_dim: 64
    block_num: 6
    group: 4
    stage: 1
    patch_expansion: 0.5
    channel_expansion: 4
  ]
  network_le_dm:[
    type: latent_encoder_gelu
    in_chans: 9
    embed_dim: 64
    block_num: 6
    group: 4
    stage: 2
    patch_expansion: 0.5
    channel_expansion: 4
  ]
  network_d:[
    type: denoising
    in_channel: 256
    out_channel: 256
    inner_channel: 512
    block_num: 4
    group: 4
    patch_expansion: 0.5
    channel_expansion: 2
  ]
  network_student:[
    type: dimo_student
    input_dim: 256
    prior_dim: 256
    output_dim: 256
    hidden_dim: 512
    num_layers: 6
    num_heads: 8
    group: 4
    patch_expansion: 0.5
    channel_expansion: 4
    use_attention: True
    dropout: 0.1
  ]
  diffusion_schedule:[
    apply_ldm: False
    schedule: linear
    timesteps: 8
    linear_start: 0.1
    linear_end: 0.99
  ]
  path:[
    pretrain_network_g: experiments/pretrained_models/stage1_generator.pth
    pretrain_network_le: experiments/pretrained_models/stage1_latent_encoder.pth
    pretrain_teacher: experiments/pretrained_models/stage2_diffusion_teacher.pth
    strict_load_g: True
    strict_load_le: True
    resume_state: None
    experiments_root: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4/experiments/DiMO_distillation
    models: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4/experiments/DiMO_distillation/models
    training_states: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4/experiments/DiMO_distillation/training_states
    log: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4/experiments/DiMO_distillation
    visualization: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4/experiments/DiMO_distillation/visualization
  ]
  train:[
    ema_decay: 0.999
    optim_g:[
      type: Adam
      lr: 0.0002
      weight_decay: 0
      betas: [0.9, 0.99]
    ]
    scheduler:[
      type: MultiStepLR
      milestones: [50000, 100000, 200000, 300000]
      gamma: 0.5
    ]
    total_iter: 400000
    warmup_iter: -1
    distillation_loss_weight: 1.0
    distillation_loss:[
      type: DiMODistillationLoss
      loss_type: mse
      temperature: 1.0
      alpha: 0.7
      beta: 0.3
      use_feature_matching: True
    ]
    pixel_loss_weight: 0.1
    pixel_loss:[
      type: L1Loss
      loss_weight: 1.0
      reduction: mean
    ]
    perceptual_loss_weight: 0.1
    perceptual_loss:[
      type: PerceptualLoss
      layer_weights:[
        conv5_4: 1
      ]
      vgg_type: vgg19
      use_input_norm: True
      range_norm: False
      perceptual_weight: 1.0
      style_weight: 0
      criterion: l1
    ]
    style_loss_weight: 0
  ]
  val:[
    val_freq: 5000.0
    save_img: False
    metrics:[
      psnr:[
        type: calculate_psnr
        crop_border: 0
        test_y_channel: False
      ]
    ]
  ]
  logger:[
    print_freq: 100
    save_checkpoint_freq: 5000.0
    use_tb_logger: True
    wandb:[
      project: None
      resume_id: None
    ]
  ]
  dist_params:[
    backend: nccl
    port: 29500
  ]
  dist: False
  rank: 0
  world_size: 1
  auto_resume: False
  is_train: True
  root_path: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/code/ours/pami/ours/ours_v4

2025-07-28 19:18:05,074 INFO: Dataset [PairedImagePolarDataset] - TrainSet is built.
2025-07-28 19:18:05,074 INFO: Training statistics:
	Number of train images: 9722
	Dataset enlarge ratio: 1
	Batch size per gpu: 2
	World size (gpu number): 1
	Require iter number per epoch: 4861
	Total epochs: 83; iters: 400000.

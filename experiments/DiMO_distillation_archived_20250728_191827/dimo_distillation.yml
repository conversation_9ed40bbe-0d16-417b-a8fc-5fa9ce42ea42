# GENERATE TIME: Mon Jul 28 19:18:04 2025
# CMD:
# train_dimo.py -opt options/train/dimo_distillation.yml

# DiMO Distillation Training Configuration
# This config is for distilling a multi-step diffusion model into a one-step generator

name: DiMO_distillation
model_type: DiMO
scale: 1
num_gpu: 1
manual_seed: 0

# Dataset configuration
datasets:
  train:
    name: TrainSet
    type: PairedImagePolarDataset
    dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
    dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/train
    filename_tmpl: '{}'
    io_backend:
      type: disk
    test_scenes: [ ]

    easy_data_ratio: 1
    hard_data_ratio: 3

    gt_size: 256
    use_hflip: true
    use_rot: true

    # Data loader
    use_shuffle: true
    num_worker_per_gpu: 4
    batch_size_per_gpu: 2
    dataset_enlarge_ratio: 1
    prefetch_mode: ~

  val:
    name: ValSet
    type: PairedImagePolarDataset
    dataroot_gt: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/val
    dataroot_lq: /ailab/user/yaomingde/workspace/ideas/polarization_reflect/data/published_raw_only/val
    io_backend:
      type: disk

# Network structures
network_g:
  type: InvNet  # or your specific generator type
  embed_dim: 64
  group: 4
  block_num: 6
  patch_expansion: 0.5
  channel_expansion: 4

network_le:
  type: latent_encoder_gelu
  in_chans: 12
  embed_dim: 64
  block_num: 6
  group: 4
  stage: 1
  patch_expansion: 0.5
  channel_expansion: 4

network_le_dm:
  type: latent_encoder_gelu
  in_chans: 9
  embed_dim: 64
  block_num: 6
  group: 4
  stage: 2
  patch_expansion: 0.5
  channel_expansion: 4

network_d:
  type: denoising
  in_channel: 256
  out_channel: 256
  inner_channel: 512
  block_num: 4
  group: 4
  patch_expansion: 0.5
  channel_expansion: 2

# DiMO Student Network (One-step generator)
network_student:
  type: dimo_student
  input_dim: 256
  prior_dim: 256
  output_dim: 256
  hidden_dim: 512
  num_layers: 6
  num_heads: 8
  group: 4
  patch_expansion: 0.5
  channel_expansion: 4
  use_attention: true
  dropout: 0.1

# Diffusion schedule for teacher model
diffusion_schedule:
  apply_ldm: false
  schedule: linear
  timesteps: 8
  linear_start: 0.1
  linear_end: 0.99

# Path configuration
path:
  pretrain_network_g: experiments/pretrained_models/stage1_generator.pth
  pretrain_network_le: experiments/pretrained_models/stage1_latent_encoder.pth
  pretrain_teacher: experiments/pretrained_models/stage2_diffusion_teacher.pth
  strict_load_g: true
  strict_load_le: true
  resume_state: ~

# Training settings
train:
  ema_decay: 0.999
  optim_g:
    type: Adam
    lr: !!float 2e-4
    weight_decay: 0
    betas: [0.9, 0.99]

  scheduler:
    type: MultiStepLR
    milestones: [50000, 100000, 200000, 300000]
    gamma: 0.5

  total_iter: 400000
  warmup_iter: -1  # no warm up

  # Losses
  distillation_loss_weight: 1.0
  distillation_loss:
    type: DiMODistillationLoss
    loss_type: mse
    temperature: 1.0
    alpha: 0.7
    beta: 0.3
    use_feature_matching: true

  pixel_loss_weight: 0.1
  pixel_loss:
    type: L1Loss
    loss_weight: 1.0
    reduction: mean

  perceptual_loss_weight: 0.1
  perceptual_loss:
    type: PerceptualLoss
    layer_weights:
      'conv5_4': 1  # before relu
    vgg_type: vgg19
    use_input_norm: true
    range_norm: false
    perceptual_weight: 1.0
    style_weight: 0
    criterion: l1

  style_loss_weight: 0

# Validation settings
val:
  val_freq: !!float 5e3
  save_img: false

  metrics:
    psnr: # metric name
      type: calculate_psnr
      crop_border: 0
      test_y_channel: false

# Logging settings
logger:
  print_freq: 100
  save_checkpoint_freq: !!float 5e3
  use_tb_logger: true
  wandb:
    project: ~
    resume_id: ~

# Distributed training settings
dist_params:
  backend: nccl
  port: 29500

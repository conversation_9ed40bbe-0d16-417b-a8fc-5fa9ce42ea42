#!/usr/bin/env python3
"""
Simplified DiMO Training Script
A simplified version that bypasses the make_exp_dirs issue.
"""

import os
import logging
import torch
from os import path as osp

from basicsr.data import build_dataloader, build_dataset
from basicsr.utils import get_root_logger, get_time_str, set_random_seed
from basicsr.utils.dist_util import get_dist_info, init_dist
from basicsr.utils.options import dict2str

from polarfree.utils.options import parse_options
from polarfree.models import build_model


def create_directories_manually(opt):
    """Manually create experiment directories."""
    directories = [
        opt['path']['experiments_root'],
        opt['path']['models'],
        opt['path']['training_states'],
        opt['path']['log'],
        opt['path']['visualization']
    ]
    
    for directory in directories:
        os.makedirs(directory, exist_ok=True)
        print(f"Created directory: {directory}")


def main():
    # Parse options
    root_path = osp.abspath(osp.join(__file__, osp.pardir))
    opt, args = parse_options(root_path, is_train=True)
    opt['root_path'] = root_path
    
    # Distributed training settings
    if opt['dist']:
        init_dist('nccl')
    rank, world_size = get_dist_info()
    opt['rank'] = rank
    opt['world_size'] = world_size
    
    # Create directories manually
    create_directories_manually(opt)
    
    # Setup logger
    log_file = osp.join(opt['path']['log'], f"train_{opt['name']}_{get_time_str()}.log")
    logger = get_root_logger(logger_name='basicsr', log_level=logging.INFO, log_file=log_file)
    logger.info(dict2str(opt))
    
    # Set random seed
    seed = opt['manual_seed']
    if seed is None:
        seed = 0
        opt['manual_seed'] = seed
    set_random_seed(seed + rank)
    
    torch.backends.cudnn.benchmark = True
    
    # Create datasets
    logger.info("Creating datasets...")
    train_set = build_dataset(opt['datasets']['train'])
    logger.info(f'Number of train images: {len(train_set)}')
    
    # Create dataloader
    train_loader = build_dataloader(
        train_set,
        opt['datasets']['train'],
        num_gpu=opt['num_gpu'],
        dist=opt['dist'],
        sampler=None,
        seed=opt['manual_seed']
    )
    
    # Create model
    logger.info("Creating DiMO model...")
    model = build_model(opt)
    logger.info("DiMO model created successfully!")
    
    # Test a few iterations
    logger.info("Testing training loop...")
    model.train()
    
    data_iter = iter(train_loader)
    for i in range(3):  # Test 3 iterations
        try:
            data = next(data_iter)
            logger.info(f"Iteration {i+1}: Processing batch with keys: {list(data.keys())}")
            
            # Feed data to model
            model.feed_data(data)
            logger.info(f"Iteration {i+1}: Data fed successfully")
            
            # Optimize parameters
            model.optimize_parameters(i+1)
            logger.info(f"Iteration {i+1}: Optimization completed")
            
            # Get current log
            log_vars = model.get_current_log()
            logger.info(f"Iteration {i+1}: Loss values: {log_vars}")
            
        except Exception as e:
            logger.error(f"Error in iteration {i+1}: {str(e)}")
            import traceback
            traceback.print_exc()
            break
    
    logger.info("DiMO training test completed!")


if __name__ == '__main__':
    main()

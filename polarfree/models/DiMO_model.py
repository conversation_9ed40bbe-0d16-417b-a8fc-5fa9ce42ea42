"""
DiMO模型(Distilling Masked Diffusion Models into One-step Generator)

核心逻辑:
1. DiMO是一个知识蒸馏框架，将多步扩散模型(Teacher)蒸馏为单步生成器(Student)
2. 模型结构:
   - 教师模型: 多步扩散模型，包含net_le_dm(特征提取器)和net_d(去噪网络)
   - 学生模型: 单步生成器，通过net_student直接从输入生成结果
   - 共享网络: net_g(生成器)和net_le(特征提取器)

运行顺序:
1. 初始化 (init): 
   - 创建教师和学生模型
   - 加载预训练权重
   - 设置优化器和损失函数

2. 训练流程 (optimize_parameters):
   - 冻结教师模型，只优化学生模型
   - 教师模型前向传播(teacher_forward)：生成多步扩散的结果
   - 学生模型前向传播(student_forward)：生成单步结果
   - 计算蒸馏损失：使学生模型输出与教师模型输出匹配
   - 反向传播，更新学生模型参数

3. 测试/推理 (test):
   - 只使用学生模型和共享网络进行推理
   - 单步生成结果，无需多步扩散过程

主要函数说明:
- _init_networks: 初始化所有网络组件
- _setup_teacher_student: 设置教师和学生模型
- _load_pretrained_models: 加载预训练模型
- teacher_forward: 教师模型(多步扩散)的前向传播
- student_forward: 学生模型(单步生成)的前向传播
- compute_distillation_loss: 计算教师和学生模型输出的蒸馏损失
- optimize_parameters: 训练学生模型的主函数
- test: 推理函数，使用学生模型生成结果
- set_new_noise_schedule: 设置扩散模型的噪声调度
- q_sample, p_sample_loop_wo_variance: 扩散模型采样相关函数

数据流向:
输入极化图像 -> 特征提取(extract_polarization_features) -> 
教师&学生模型处理 -> 计算损失 -> 更新学生模型 -> 最终输出

输入数据:
- 极化图像: lq_img0, lq_img45, lq_img90, lq_img135
- RGB图像: lq_rgb
- 极化属性: lq_aolp, lq_dolp, lq_Ip, lq_Inp
- 真值图像: gt_rgb (训练时)
"""

import torch
import torch.nn as nn
import torch.nn.functional as F
from torch.distributions import Categorical
import numpy as np
from collections import OrderedDict
from copy import deepcopy
from os import path as osp
from tqdm import tqdm

from basicsr.utils import imwrite, tensor2img
from basicsr.metrics import calculate_metric

from basicsr.utils.registry import MODEL_REGISTRY
from basicsr.utils import get_root_logger
from basicsr.archs import build_network
from basicsr.losses import build_loss

from polarfree.utils.base_model import BaseModel
from polarfree.losses import build_loss as build_polarfree_loss
from ldm.ddpm import DDPM
from ldm.util import default


def extract_into_tensor(a, t, x_shape):
    """Extract values from a tensor at given timesteps and reshape to match x_shape."""
    b, *_ = t.shape
    out = a.gather(-1, t)
    return out.reshape(b, *((1,) * (len(x_shape) - 1)))


@MODEL_REGISTRY.register()
class DiMO(BaseModel):
    """DiMO: Distilling Masked Diffusion Models into One-step Generator
    
    This model implements the DiMO distillation framework that converts
    multi-step diffusion models into single-step generators.
    """
    
    def __init__(self, opt):
        super(DiMO, self).__init__(opt)
        self._init_networks(opt)
        self._load_pretrained_models()
        
        # Setup teacher and student models
        self._setup_teacher_student()
        
        if self.is_train:
            self.init_training_settings()
    
    def _init_networks(self, opt):
        """Initialize networks."""
        # Stage 1 networks (shared between teacher and student)
        self.net_g = build_network(opt['network_g'])
        self.net_g = self.model_to_device(self.net_g)
        self.print_network(self.net_g)
        
        self.net_le = build_network(opt['network_le'])
        self.net_le = self.model_to_device(self.net_le)
        self.print_network(self.net_le)
        
        # Stage 2 networks for teacher (diffusion model)
        self.net_le_dm = build_network(opt['network_le_dm'])
        self.net_le_dm = self.model_to_device(self.net_le_dm)
        self.print_network(self.net_le_dm)
        
        self.net_d = build_network(opt['network_d'])
        self.net_d = self.model_to_device(self.net_d)
        self.print_network(self.net_d)
        
        # Student network (one-step generator)
        self.net_student = build_network(opt['network_student'])
        self.net_student = self.model_to_device(self.net_student)
        self.print_network(self.net_student)
    
    def _setup_teacher_student(self):
        """Setup teacher (diffusion) and student (one-step) models."""
        # Teacher model: multi-step diffusion
        self.apply_ldm = self.opt['diffusion_schedule'].get('apply_ldm', False)
        
        if self.apply_ldm:
            # Use LDM implementation for teacher
            self.teacher_diffusion = DDPM(
                denoise=self.net_d,
                condition=self.net_le_dm,
                n_feats=self.opt['network_g']['embed_dim'],
                group=self.opt['network_g']['group'],
                linear_start=self.opt['diffusion_schedule']['linear_start'],
                linear_end=self.opt['diffusion_schedule']['linear_end'],
                timesteps=self.opt['diffusion_schedule']['timesteps']
            )
            self.teacher_diffusion = self.model_to_device(self.teacher_diffusion)
        else:
            # Use local diffusion implementation
            self.set_new_noise_schedule(self.opt['diffusion_schedule'], self.device)
        
        # Student model: direct one-step generator
        # The student network directly maps from input to output without diffusion steps
    
    def _load_pretrained_models(self):
        """Load pretrained models."""
        logger = get_root_logger()

        # Load stage 1 pretrained model
        load_path = self.opt['path'].get('pretrain_network_g', None)
        if load_path is not None and load_path != '~':
            param_key = self.opt['path'].get('param_key_g', 'params')
            self.load_network(self.net_g, load_path, self.opt['path'].get('strict_load_g', True), param_key)
            logger.info(f'Loaded pretrained network_g from {load_path}')
        else:
            logger.info('No pretrained network_g provided, training from scratch')

        load_path = self.opt['path'].get('pretrain_network_le', None)
        if load_path is not None and load_path != '~':
            param_key = self.opt['path'].get('param_key_le', 'params')
            self.load_network(self.net_le, load_path, self.opt['path'].get('strict_load_le', True), param_key)
            logger.info(f'Loaded pretrained network_le from {load_path}')
        else:
            logger.info('No pretrained network_le provided, training from scratch')

        # Load teacher model (diffusion) pretrained weights
        load_path = self.opt['path'].get('pretrain_teacher', None)
        if load_path is not None and load_path != '~':
            self.load_teacher_model(load_path)
            logger.info(f'Loaded pretrained teacher model from {load_path}')
        else:
            logger.warning('No pretrained teacher model provided. DiMO requires a trained teacher model for distillation.')
            logger.warning('The teacher model will be initialized randomly, which may lead to poor distillation results.')
    
    def load_teacher_model(self, load_path):
        """Load pretrained teacher diffusion model."""
        logger = get_root_logger()
        logger.info(f'Loading teacher model from {load_path}')
        
        checkpoint = torch.load(load_path, map_location=lambda storage, loc: storage)
        
        # Load diffusion model components
        if 'net_le_dm' in checkpoint:
            self.load_network(self.net_le_dm, checkpoint['net_le_dm'], strict=True)
        if 'net_d' in checkpoint:
            self.load_network(self.net_d, checkpoint['net_d'], strict=True)
    
    def init_training_settings(self):
        """Initialize training settings."""
        self.net_g.train()
        self.net_le.train()
        self.net_student.train()
        
        # Freeze teacher model during distillation
        self.net_le_dm.eval()
        self.net_d.eval()
        for param in self.net_le_dm.parameters():
            param.requires_grad = False
        for param in self.net_d.parameters():
            param.requires_grad = False
        
        train_opt = self.opt['train']
        
        # Setup optimizers
        self.setup_optimizers()
        self.setup_schedulers()
        
        # Setup losses
        if train_opt.get('distillation_loss_weight', 0) > 0:
            self.cri_distillation = build_polarfree_loss(train_opt['distillation_loss']).to(self.device)
        else:
            self.cri_distillation = None

        if train_opt.get('pixel_loss_weight', 0) > 0:
            self.cri_pixel = build_loss(train_opt['pixel_loss']).to(self.device)
        else:
            self.cri_pixel = None

        if train_opt.get('perceptual_loss_weight', 0) > 0:
            self.cri_perceptual = build_loss(train_opt['perceptual_loss']).to(self.device)
        else:
            self.cri_perceptual = None
    
    def setup_optimizers(self):
        """Setup optimizers for student model."""
        train_opt = self.opt['train']
        
        # Only optimize student model parameters
        optim_params = []
        for k, v in self.net_student.named_parameters():
            if v.requires_grad:
                optim_params.append(v)
        
        optim_type = train_opt['optim_g'].pop('type')
        self.optimizer_g = self.get_optimizer(optim_type, optim_params, **train_opt['optim_g'])
        self.optimizers.append(self.optimizer_g)
    
    def feed_data(self, data):
        """Feed data to the model."""
        # Load polarization images (matching existing models)
        self.lq_img0 = data['lq_img0'].to(self.device)
        self.lq_img45 = data['lq_img45'].to(self.device)
        self.lq_img90 = data['lq_img90'].to(self.device)
        self.lq_img135 = data['lq_img135'].to(self.device)
        self.lq_rgb = data['lq_rgb'].to(self.device)
        self.lq_aolp = data['lq_aolp'].to(self.device)
        self.lq_dolp = data['lq_dolp'].to(self.device)
        self.lq_Ip = data['lq_Ip'].to(self.device)
        self.lq_Inp = data['lq_Inp'].to(self.device)

        # Load ground truth
        if 'gt_rgb' in data:
            self.gt_rgb = data['gt_rgb'].to(self.device)

        # Extract polarization features
        self.input_features = self.extract_polarization_features()
    
    def extract_polarization_features(self):
        """Extract polarization features from input data."""
        # Extract all polarization components like in existing models
        input_features = [
            self.lq_rgb, self.lq_img0, self.lq_img45,
            self.lq_img90, self.lq_img135, self.lq_aolp, self.lq_dolp
        ]
        return input_features

    def teacher_forward(self, input_features, gt_rgb=None):
        """Forward pass through teacher model (multi-step diffusion)."""
        with torch.no_grad():
            # Get prior from stage 1 latent encoder
            prior_z = self.net_le(input_features, gt_rgb)

            # Process through teacher diffusion model
            if self.apply_ldm:
                # Use LDM implementation
                teacher_output, _ = self.teacher_diffusion(input_features, prior_z)
            else:
                # Use local implementation
                prior_d = self.net_le_dm(input_features)

                # Diffusion forward process
                t = self.opt['diffusion_schedule']['timesteps']
                noise = torch.randn_like(prior_z)
                prior_noisy = self.q_sample(
                    x_start=prior_z,
                    sqrt_alpha_cumprod=self.alphas_cumprod[t-1],
                    noise=noise
                )

                # Diffusion reverse process
                teacher_output = self.p_sample_loop_wo_variance(prior_d, prior_noisy)

            return teacher_output

    def student_forward(self, input_features, gt_rgb=None):
        """Forward pass through student model (one-step generator)."""
        # Get prior from stage 1 latent encoder
        prior_z = self.net_le(input_features, gt_rgb)

        # Concatenate input features if they are in list format
        if isinstance(input_features, list):
            input_features_cat = torch.cat(input_features, dim=1)
        else:
            input_features_cat = input_features

        # Flatten image features to match prior_z dimensions
        # input_features_cat: [B, C, H, W] -> [B, seq_len, C]
        B, C, H, W = input_features_cat.shape
        # Use adaptive pooling to match prior_z sequence length
        input_features_pooled = torch.nn.functional.adaptive_avg_pool2d(
            input_features_cat, (4, 4)  # Match the 4x4=16 sequence length of prior_z
        )
        input_features_flat = input_features_pooled.view(B, C, 16).transpose(1, 2)  # [B, 16, C]

        # Student model takes input features and prior features separately
        student_output = self.net_student(input_features_flat, prior_z)

        return student_output

    def compute_distillation_loss(self, teacher_output, student_output):
        """Compute distillation loss between teacher and student outputs."""
        if self.cri_distillation is None:
            return 0

        # Distribution matching loss
        # This is the core of DiMO - matching output distributions
        loss_distill = self.cri_distillation(student_output, teacher_output.detach())

        return loss_distill

    def optimize_parameters(self, current_iter):
        """Optimize student model parameters."""
        self.optimizer_g.zero_grad()

        # Teacher forward (frozen)
        gt_rgb = getattr(self, 'gt_rgb', None)
        teacher_output = self.teacher_forward(self.input_features, gt_rgb)

        # Student forward
        student_output = self.student_forward(self.input_features, gt_rgb)

        # Compute losses
        l_total = 0
        loss_dict = OrderedDict()

        # Distillation loss (main loss)
        if self.cri_distillation:
            l_distill = self.compute_distillation_loss(teacher_output, student_output)
            l_total += l_distill * self.opt['train']['distillation_loss_weight']
            loss_dict['l_distill'] = l_distill

        # Pixel loss (auxiliary)
        if self.cri_pixel and hasattr(self, 'gt_rgb'):
            # Generate final output through stage 1 generator
            if self.opt['network_g']['type'] == 'InvNet':
                final_output, _ = self.net_g(self.lq_rgb, student_output, False)
            else:
                final_output = self.net_g(self.lq_rgb, student_output)
            l_pixel = self.cri_pixel(final_output, self.gt_rgb)
            l_total += l_pixel * self.opt['train']['pixel_loss_weight']
            loss_dict['l_pixel'] = l_pixel

        # Perceptual loss (auxiliary)
        if self.cri_perceptual and hasattr(self, 'gt_rgb'):
            final_output = self.net_g(self.lq_rgb, student_output)
            l_percep, l_style = self.cri_perceptual(final_output, self.gt_rgb)
            if l_percep is not None:
                l_total += l_percep * self.opt['train']['perceptual_loss_weight']
                loss_dict['l_percep'] = l_percep
            if l_style is not None:
                l_total += l_style * self.opt['train']['style_loss_weight']
                loss_dict['l_style'] = l_style

        l_total.backward()
        self.optimizer_g.step()

        self.log_dict = self.reduce_loss_dict(loss_dict)

    def test(self):
        """Test function for inference."""
        self.net_g.eval()
        self.net_le.eval()
        self.net_student.eval()

        with torch.no_grad():
            # One-step generation through student model
            student_output = self.student_forward(self.input_features)

            # Generate final output
            if self.opt['network_g']['type'] == 'InvNet':
                self.output, _ = self.net_g(self.lq_rgb, student_output, False)
            else:
                self.output = self.net_g(self.lq_rgb, student_output)

        self.net_g.train()
        self.net_le.train()
        self.net_student.train()

    def get_current_visuals(self):
        """Get current visual results."""
        out_dict = OrderedDict()
        out_dict['lq'] = self.lq_rgb.detach().cpu()
        out_dict['result'] = self.output.detach().cpu()
        if hasattr(self, 'gt_rgb'):
            out_dict['gt'] = self.gt_rgb.detach().cpu()
        return out_dict

    def save(self, epoch, current_iter):
        """Save networks and training states."""
        self.save_network([self.net_g, self.net_le, self.net_student], 'net_g', current_iter)
        self.save_training_state(epoch, current_iter)

    def get_current_log(self):
        """Get current log dict."""
        return self.log_dict

    def nondist_validation(self, dataloader, current_iter, tb_logger, save_img):
        """Non-distributed validation."""
        dataset_name = dataloader.dataset.opt['name']
        with_metrics = self.opt['val'].get('metrics') is not None
        use_pbar = self.opt['val'].get('pbar', False)

        if with_metrics:
            if not hasattr(self, 'metric_results'):  # only execute in the first run
                self.metric_results = {metric: 0 for metric in self.opt['val']['metrics'].keys()}
            # initialize the best metric results for each dataset_name (supporting multiple validation datasets)
            self._initialize_best_metric_results(dataset_name)
        # zero the metric results
        if with_metrics:
            self.metric_results = {metric: 0 for metric in self.metric_results}

        metric_data = dict()
        if use_pbar:
            pbar = tqdm(total=len(dataloader), unit='image')

        for idx, val_data in enumerate(dataloader):
            img_name = osp.splitext(osp.basename(val_data['lq_path'][0]))[0]
            self.feed_data(val_data)
            self.test()

            visuals = self.get_current_visuals()
            sr_img = tensor2img([visuals['result']])
            metric_data['img'] = sr_img
            if 'gt' in visuals:
                gt_img = tensor2img([visuals['gt']])
                metric_data['img2'] = gt_img
                del self.gt_rgb

            # tentative for out of GPU memory
            del self.lq_rgb
            del self.output
            torch.cuda.empty_cache()

            if save_img:
                if self.opt['is_train']:
                    save_img_path = osp.join(self.opt['path']['visualization'], img_name,
                                           f'{img_name}_{current_iter}.png')
                else:
                    if self.opt['val']['suffix']:
                        save_img_path = osp.join(self.opt['path']['visualization'], dataset_name,
                                               f'{img_name}_{self.opt["val"]["suffix"]}.png')
                    else:
                        save_img_path = osp.join(self.opt['path']['visualization'], dataset_name,
                                               f'{img_name}_{self.opt["name"]}.png')
                imwrite(sr_img, save_img_path)

            if with_metrics:
                # calculate metrics
                for name, opt_ in self.opt['val']['metrics'].items():
                    self.metric_results[name] += calculate_metric(metric_data, opt_)
            if use_pbar:
                pbar.update(1)
                pbar.set_description(f'Test {img_name}')

        if use_pbar:
            pbar.close()

        if with_metrics:
            for metric in self.metric_results.keys():
                self.metric_results[metric] /= (idx + 1)
                # update the best metric result
                self._update_best_metric_result(dataset_name, metric, self.metric_results[metric], current_iter)

            self._log_validation_metric_values(current_iter, dataset_name, tb_logger)

    def set_new_noise_schedule(self, schedule_opt, device):
        """Set noise schedule for local diffusion implementation."""
        to_torch = lambda x: torch.tensor(x).clone().detach().to(torch.float32).to(device)

        betas = make_beta_schedule(
            schedule=schedule_opt['schedule'],
            n_timestep=schedule_opt['timesteps'],
            linear_start=schedule_opt['linear_start'],
            linear_end=schedule_opt['linear_end']
        )
        betas = betas.detach().cpu().numpy() if isinstance(betas, torch.Tensor) else betas
        alphas = 1. - betas
        alphas_cumprod = np.cumprod(alphas, axis=0)
        alphas_cumprod_prev = np.append(1., alphas_cumprod[:-1])

        timesteps, = betas.shape
        self.num_timesteps = int(timesteps)
        self.register_buffer('betas', to_torch(betas))
        self.register_buffer('alphas_cumprod', to_torch(alphas_cumprod))
        self.register_buffer('alphas_cumprod_prev', to_torch(alphas_cumprod_prev))

        # calculations for diffusion q(x_t | x_{t-1}) and others
        self.register_buffer('sqrt_alphas_cumprod', to_torch(np.sqrt(alphas_cumprod)))
        self.register_buffer('sqrt_one_minus_alphas_cumprod', to_torch(np.sqrt(1. - alphas_cumprod)))
        self.register_buffer('log_one_minus_alphas_cumprod', to_torch(np.log(1. - alphas_cumprod)))
        self.register_buffer('sqrt_recip_alphas_cumprod', to_torch(np.sqrt(1. / alphas_cumprod)))
        self.register_buffer('sqrt_recipm1_alphas_cumprod', to_torch(np.sqrt(1. / alphas_cumprod - 1)))

    def q_sample(self, x_start, sqrt_alpha_cumprod, noise=None):
        """Sample from q(x_t | x_0)."""
        noise = default(noise, lambda: torch.randn_like(x_start))
        return (sqrt_alpha_cumprod * x_start +
                torch.sqrt(1 - sqrt_alpha_cumprod**2) * noise)

    def p_sample_loop_wo_variance(self, condition, shape):
        """Sampling loop without variance (deterministic)."""
        device = self.betas.device
        b = shape.shape[0]

        # Start from pure noise
        img = torch.randn(shape.shape, device=device)

        # Reverse diffusion process
        for i in reversed(range(0, self.num_timesteps)):
            t = torch.full((b,), i, device=device, dtype=torch.long)
            img = self.p_sample_wo_variance(img, t, condition)

        return img

    def p_sample_wo_variance(self, x, t, condition):
        """Single step sampling without variance."""
        # Expand time tensor to match x dimensions
        if len(t.shape) == 1:
            # t is [batch_size], need to expand to [batch_size, seq_len, feature_dim]
            # The denoising network expects time to have the same feature dimension as x
            t_expanded = t.unsqueeze(-1).unsqueeze(-1).expand(x.shape[0], x.shape[1], x.shape[2])
        else:
            t_expanded = t

        # Predict noise
        noise_pred = self.net_d(x, condition, t_expanded)

        # Compute x_{t-1}
        alpha_t = extract_into_tensor(self.alphas_cumprod, t, x.shape)
        sqrt_one_minus_alpha_t = extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t, x.shape)

        # Predict x_0
        pred_x0 = (x - sqrt_one_minus_alpha_t * noise_pred) / torch.sqrt(alpha_t)

        if t[0] == 0:
            return pred_x0
        else:
            alpha_prev = extract_into_tensor(self.alphas_cumprod_prev, t, x.shape)
            sqrt_alpha_prev = torch.sqrt(alpha_prev)
            sqrt_one_minus_alpha_prev = extract_into_tensor(self.sqrt_one_minus_alphas_cumprod, t-1, x.shape)

            # Direction pointing to x_t
            dir_xt = sqrt_one_minus_alpha_prev * noise_pred
            x_prev = sqrt_alpha_prev * pred_x0 + dir_xt

            return x_prev


def make_beta_schedule(schedule, n_timestep, linear_start=1e-4, linear_end=2e-2, cosine_s=8e-3):
    """Create beta schedule for diffusion."""
    if schedule == "linear":
        betas = (
            torch.linspace(linear_start ** 0.5, linear_end ** 0.5, n_timestep, dtype=torch.float64) ** 2
        )
    elif schedule == "cosine":
        timesteps = (
            torch.arange(n_timestep + 1, dtype=torch.float64) / n_timestep + cosine_s
        )
        alphas = timesteps / (1 + cosine_s) * np.pi / 2
        alphas = torch.cos(alphas).pow(2)
        alphas = alphas / alphas[0]
        betas = 1 - alphas[1:] / alphas[:-1]
        betas = np.clip(betas, 0, 0.999)
    else:
        raise ValueError(f"schedule '{schedule}' unknown.")
    return betas

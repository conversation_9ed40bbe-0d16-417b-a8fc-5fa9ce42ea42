import torch
import torch.nn as nn
import torch.nn.functional as F
from basicsr.utils.registry import LOSS_REGISTRY


@LOSS_REGISTRY.register()
class DiMODistillationLoss(nn.Module):
    """DiMO Distillation Loss for matching teacher and student output distributions.
    
    This loss implements the core idea of DiMO: matching the output distribution
    of the teacher model (multi-step diffusion) with the student model (one-step generator).
    """
    
    def __init__(self, 
                 loss_type='mse',
                 temperature=1.0,
                 alpha=0.7,
                 beta=0.3,
                 use_feature_matching=True,
                 feature_layers=None):
        """
        Args:
            loss_type (str): Type of base loss ('mse', 'l1', 'kl', 'js')
            temperature (float): Temperature for softmax in KL/JS divergence
            alpha (float): Weight for output matching loss
            beta (float): Weight for feature matching loss
            use_feature_matching (bool): Whether to use intermediate feature matching
            feature_layers (list): Layers to extract features from (if applicable)
        """
        super(DiMODistillationLoss, self).__init__()
        
        self.loss_type = loss_type
        self.temperature = temperature
        self.alpha = alpha
        self.beta = beta
        self.use_feature_matching = use_feature_matching
        
        # Base loss functions
        if loss_type == 'mse':
            self.base_loss = nn.MSELoss()
        elif loss_type == 'l1':
            self.base_loss = nn.L1Loss()
        elif loss_type == 'smooth_l1':
            self.base_loss = nn.SmoothL1Loss()
        else:
            self.base_loss = nn.MSELoss()  # Default
    
    def forward(self, student_output, teacher_output, student_features=None, teacher_features=None):
        """
        Args:
            student_output: Output from student model
            teacher_output: Output from teacher model (detached)
            student_features: Intermediate features from student (optional)
            teacher_features: Intermediate features from teacher (optional)
        """
        total_loss = 0.0
        
        # Main output matching loss
        if self.loss_type in ['mse', 'l1', 'smooth_l1']:
            output_loss = self.base_loss(student_output, teacher_output)
        elif self.loss_type == 'kl':
            output_loss = self.kl_divergence_loss(student_output, teacher_output)
        elif self.loss_type == 'js':
            output_loss = self.js_divergence_loss(student_output, teacher_output)
        else:
            output_loss = self.base_loss(student_output, teacher_output)
        
        total_loss += self.alpha * output_loss
        
        # Feature matching loss (if enabled and features provided)
        if (self.use_feature_matching and 
            student_features is not None and 
            teacher_features is not None):
            feature_loss = self.feature_matching_loss(student_features, teacher_features)
            total_loss += self.beta * feature_loss
        
        return total_loss
    
    def kl_divergence_loss(self, student_output, teacher_output):
        """KL divergence loss between student and teacher distributions."""
        # Convert to probability distributions
        student_prob = F.softmax(student_output / self.temperature, dim=-1)
        teacher_prob = F.softmax(teacher_output / self.temperature, dim=-1)
        
        # KL divergence
        kl_loss = F.kl_div(
            F.log_softmax(student_output / self.temperature, dim=-1),
            teacher_prob,
            reduction='batchmean'
        )
        
        return kl_loss * (self.temperature ** 2)
    
    def js_divergence_loss(self, student_output, teacher_output):
        """Jensen-Shannon divergence loss."""
        # Convert to probability distributions
        student_prob = F.softmax(student_output / self.temperature, dim=-1)
        teacher_prob = F.softmax(teacher_output / self.temperature, dim=-1)
        
        # Average distribution
        avg_prob = 0.5 * (student_prob + teacher_prob)
        
        # JS divergence = 0.5 * KL(P||M) + 0.5 * KL(Q||M)
        js_loss = 0.5 * F.kl_div(F.log_softmax(student_output / self.temperature, dim=-1), 
                                  avg_prob, reduction='batchmean')
        js_loss += 0.5 * F.kl_div(F.log_softmax(teacher_output / self.temperature, dim=-1), 
                                   avg_prob, reduction='batchmean')
        
        return js_loss * (self.temperature ** 2)
    
    def feature_matching_loss(self, student_features, teacher_features):
        """Feature matching loss for intermediate representations."""
        if isinstance(student_features, (list, tuple)):
            # Multiple feature layers
            feature_loss = 0.0
            for s_feat, t_feat in zip(student_features, teacher_features):
                feature_loss += self.base_loss(s_feat, t_feat.detach())
            return feature_loss / len(student_features)
        else:
            # Single feature layer
            return self.base_loss(student_features, teacher_features.detach())


@LOSS_REGISTRY.register()
class DiMOAdversarialLoss(nn.Module):
    """Adversarial loss component for DiMO distillation.
    
    This can be used as an additional loss to improve the quality of
    the student model's output distribution.
    """
    
    def __init__(self, discriminator_weight=0.1):
        super(DiMOAdversarialLoss, self).__init__()
        self.discriminator_weight = discriminator_weight
        self.adversarial_loss = nn.BCEWithLogitsLoss()
    
    def forward(self, discriminator_output, is_real=True):
        """
        Args:
            discriminator_output: Output from discriminator
            is_real: Whether the input should be classified as real
        """
        if is_real:
            target = torch.ones_like(discriminator_output)
        else:
            target = torch.zeros_like(discriminator_output)
        
        return self.discriminator_weight * self.adversarial_loss(discriminator_output, target)


@LOSS_REGISTRY.register()
class DiMOConsistencyLoss(nn.Module):
    """Consistency loss for DiMO to ensure stable training.
    
    This loss encourages the student model to produce consistent outputs
    for similar inputs, improving training stability.
    """
    
    def __init__(self, consistency_weight=0.1, noise_std=0.01):
        super(DiMOConsistencyLoss, self).__init__()
        self.consistency_weight = consistency_weight
        self.noise_std = noise_std
        self.consistency_loss = nn.MSELoss()
    
    def forward(self, student_model, input_features, prior_features):
        """
        Args:
            student_model: The student model
            input_features: Input features
            prior_features: Prior features
        """
        # Original output
        original_output = student_model(input_features, prior_features)
        
        # Add small noise to inputs
        noise_input = input_features + torch.randn_like(input_features) * self.noise_std
        noise_prior = prior_features + torch.randn_like(prior_features) * self.noise_std
        
        # Noisy output
        noisy_output = student_model(noise_input, noise_prior)
        
        # Consistency loss
        consistency_loss = self.consistency_loss(original_output, noisy_output)
        
        return self.consistency_weight * consistency_loss


@LOSS_REGISTRY.register()
class DiMOCombinedLoss(nn.Module):
    """Combined loss for DiMO training that includes multiple loss components."""
    
    def __init__(self,
                 distillation_weight=1.0,
                 pixel_weight=0.1,
                 perceptual_weight=0.1,
                 consistency_weight=0.05,
                 distillation_config=None,
                 pixel_config=None,
                 perceptual_config=None,
                 consistency_config=None):
        super(DiMOCombinedLoss, self).__init__()
        
        self.distillation_weight = distillation_weight
        self.pixel_weight = pixel_weight
        self.perceptual_weight = perceptual_weight
        self.consistency_weight = consistency_weight
        
        # Initialize loss components
        distillation_config = distillation_config or {}
        self.distillation_loss = DiMODistillationLoss(**distillation_config)
        
        if pixel_weight > 0:
            pixel_config = pixel_config or {'loss_type': 'l1'}
            if pixel_config.get('loss_type') == 'l1':
                self.pixel_loss = nn.L1Loss()
            else:
                self.pixel_loss = nn.MSELoss()
        
        if consistency_weight > 0:
            consistency_config = consistency_config or {}
            self.consistency_loss = DiMOConsistencyLoss(**consistency_config)
    
    def forward(self, student_output, teacher_output, gt=None, 
                student_model=None, input_features=None, prior_features=None):
        """
        Args:
            student_output: Output from student model
            teacher_output: Output from teacher model
            gt: Ground truth (optional, for pixel loss)
            student_model: Student model (for consistency loss)
            input_features: Input features (for consistency loss)
            prior_features: Prior features (for consistency loss)
        """
        total_loss = 0.0
        loss_dict = {}
        
        # Distillation loss (main)
        distill_loss = self.distillation_loss(student_output, teacher_output)
        total_loss += self.distillation_weight * distill_loss
        loss_dict['distillation'] = distill_loss
        
        # Pixel loss (if GT available)
        if self.pixel_weight > 0 and gt is not None:
            pixel_loss = self.pixel_loss(student_output, gt)
            total_loss += self.pixel_weight * pixel_loss
            loss_dict['pixel'] = pixel_loss
        
        # Consistency loss
        if (self.consistency_weight > 0 and 
            student_model is not None and 
            input_features is not None and 
            prior_features is not None):
            consistency_loss = self.consistency_loss(student_model, input_features, prior_features)
            total_loss += consistency_loss
            loss_dict['consistency'] = consistency_loss
        
        return total_loss, loss_dict


if __name__ == '__main__':
    # Test the loss functions
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    # Test data
    batch_size, seq_len, dim = 2, 16, 256
    student_output = torch.randn(batch_size, seq_len, dim).to(device)
    teacher_output = torch.randn(batch_size, seq_len, dim).to(device)
    
    # Test distillation loss
    distill_loss = DiMODistillationLoss(loss_type='mse')
    loss_value = distill_loss(student_output, teacher_output)
    print(f"Distillation loss: {loss_value.item():.4f}")
    
    # Test combined loss
    combined_loss = DiMOCombinedLoss()
    total_loss, loss_dict = combined_loss(student_output, teacher_output)
    print(f"Combined loss: {total_loss.item():.4f}")
    print(f"Loss components: {loss_dict}")
    
    print("DiMO loss functions test passed!")

import torch
import torch.nn as nn
import torch.nn.functional as F
from basicsr.utils.registry import ARCH_REGISTRY


class MLP(nn.Module):
    """Multi-Layer Perceptron block for DiMO student network."""
    
    def __init__(self,
                 num_patches,
                 embed_dims,
                 patch_expansion,
                 channel_expansion,
                 **kwargs):
        super(MLP, self).__init__()

        patch_mix_dims = int(patch_expansion * num_patches)
        channel_mix_dims = int(channel_expansion * embed_dims)

        self.patch_mixer = nn.Sequential(
            nn.Linear(num_patches, patch_mix_dims),
            nn.GELU(),
            nn.Linear(patch_mix_dims, num_patches),
        )

        self.channel_mixer = nn.Sequential(
            nn.Linear(embed_dims, channel_mix_dims),
            nn.GELU(),
            nn.Linear(channel_mix_dims, embed_dims),
        )
        self.norm1 = nn.LayerNorm(embed_dims)
        self.norm2 = nn.LayerNorm(embed_dims)

    def forward(self, x):
        x = x + self.patch_mixer(self.norm1(x).transpose(1, 2)).transpose(1, 2)
        x = x + self.channel_mixer(self.norm2(x))
        return x


class AttentionBlock(nn.Module):
    """Self-attention block for better feature modeling."""
    
    def __init__(self, embed_dim, num_heads=8, dropout=0.1):
        super(AttentionBlock, self).__init__()
        self.attention = nn.MultiheadAttention(embed_dim, num_heads, dropout=dropout, batch_first=True)
        self.norm1 = nn.LayerNorm(embed_dim)
        self.norm2 = nn.LayerNorm(embed_dim)
        self.ffn = nn.Sequential(
            nn.Linear(embed_dim, embed_dim * 4),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(embed_dim * 4, embed_dim),
            nn.Dropout(dropout)
        )
    
    def forward(self, x):
        # Self-attention
        attn_out, _ = self.attention(x, x, x)
        x = self.norm1(x + attn_out)
        
        # Feed-forward
        ffn_out = self.ffn(x)
        x = self.norm2(x + ffn_out)
        
        return x


@ARCH_REGISTRY.register()
class dimo_student(nn.Module):
    """DiMO Student Network: One-step generator that directly maps input to output.
    
    This network replaces the multi-step diffusion process with a single forward pass,
    achieving similar results through knowledge distillation.
    """
    
    def __init__(
        self,
        input_dim=256,  # Input feature dimension
        prior_dim=256,  # Prior feature dimension from latent encoder
        output_dim=256,  # Output dimension
        hidden_dim=512,  # Hidden dimension
        num_layers=6,   # Number of processing layers
        num_heads=8,    # Number of attention heads
        group=4,        # Group parameter
        patch_expansion=0.5,
        channel_expansion=4,
        use_attention=True,  # Whether to use attention blocks
        dropout=0.1
    ):
        super(dimo_student, self).__init__()
        
        self.input_dim = input_dim
        self.prior_dim = prior_dim
        self.output_dim = output_dim
        self.hidden_dim = hidden_dim
        self.use_attention = use_attention
        
        # Input projection
        self.input_proj = nn.Sequential(
            nn.Linear(input_dim + prior_dim, hidden_dim),
            nn.LayerNorm(hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        # Processing layers
        self.layers = nn.ModuleList()
        for i in range(num_layers):
            if use_attention and i % 2 == 0:  # Alternate between MLP and attention
                layer = AttentionBlock(hidden_dim, num_heads, dropout)
            else:
                layer = nn.Sequential(
                    MLP(
                        num_patches=group * group,
                        embed_dims=hidden_dim,
                        patch_expansion=patch_expansion,
                        channel_expansion=channel_expansion
                    ),
                    nn.GELU(),
                    nn.Dropout(dropout)
                )
            self.layers.append(layer)
        
        # Output projection
        self.output_proj = nn.Sequential(
            nn.Linear(hidden_dim, hidden_dim // 2),
            nn.GELU(),
            nn.Dropout(dropout),
            nn.Linear(hidden_dim // 2, output_dim),
        )
        
        # Residual connection for better gradient flow
        self.residual_proj = nn.Linear(prior_dim, output_dim) if prior_dim != output_dim else nn.Identity()
        
        # Initialize weights
        self.apply(self._init_weights)
    
    def _init_weights(self, m):
        """Initialize network weights."""
        if isinstance(m, nn.Linear):
            nn.init.trunc_normal_(m.weight, std=0.02)
            if m.bias is not None:
                nn.init.constant_(m.bias, 0)
        elif isinstance(m, nn.LayerNorm):
            nn.init.constant_(m.bias, 0)
            nn.init.constant_(m.weight, 1.0)
    
    def forward(self, input_features, prior_features):
        """Forward pass of the student network.
        
        Args:
            input_features: Input features from the conditioning network
            prior_features: Prior features from the latent encoder
            
        Returns:
            Generated output features (single step)
        """
        # Concatenate input and prior features
        x = torch.cat([input_features, prior_features], dim=-1)
        
        # Input projection
        x = self.input_proj(x)
        
        # Process through layers
        for layer in self.layers:
            if isinstance(layer, AttentionBlock):
                x = layer(x)
            else:
                x = layer(x) + x  # Residual connection for MLP layers
        
        # Output projection
        output = self.output_proj(x)
        
        # Add residual connection from prior
        residual = self.residual_proj(prior_features)
        output = output + residual
        
        return output


@ARCH_REGISTRY.register()
class dimo_student_lightweight(nn.Module):
    """Lightweight version of DiMO student network for faster inference."""
    
    def __init__(
        self,
        input_dim=256,
        prior_dim=256,
        output_dim=256,
        hidden_dim=256,
        num_layers=3,
        group=4,
        patch_expansion=0.5,
        channel_expansion=2,
        dropout=0.1
    ):
        super(dimo_student_lightweight, self).__init__()
        
        # Simplified architecture for speed
        self.input_proj = nn.Sequential(
            nn.Linear(input_dim + prior_dim, hidden_dim),
            nn.GELU(),
            nn.Dropout(dropout)
        )
        
        self.layers = nn.ModuleList()
        for _ in range(num_layers):
            layer = nn.Sequential(
                MLP(
                    num_patches=group * group,
                    embed_dims=hidden_dim,
                    patch_expansion=patch_expansion,
                    channel_expansion=channel_expansion
                ),
                nn.GELU(),
            )
            self.layers.append(layer)
        
        self.output_proj = nn.Linear(hidden_dim, output_dim)
        self.residual_proj = nn.Linear(prior_dim, output_dim) if prior_dim != output_dim else nn.Identity()
    
    def forward(self, input_features, prior_features):
        x = torch.cat([input_features, prior_features], dim=-1)
        x = self.input_proj(x)
        
        for layer in self.layers:
            x = layer(x) + x
        
        output = self.output_proj(x)
        residual = self.residual_proj(prior_features)
        
        return output + residual


if __name__ == '__main__':
    # Test the student network
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    
    model = dimo_student(
        input_dim=256,
        prior_dim=256,
        output_dim=256,
        hidden_dim=512,
        num_layers=6,
        group=4
    ).to(device)
    
    # Test input
    batch_size = 2
    seq_len = 16
    input_features = torch.randn(batch_size, seq_len, 256).to(device)
    prior_features = torch.randn(batch_size, seq_len, 256).to(device)
    
    with torch.no_grad():
        output = model(input_features, prior_features)
    
    print(f"Input shape: {input_features.shape}")
    print(f"Prior shape: {prior_features.shape}")
    print(f"Output shape: {output.shape}")
    print("DiMO student network test passed!")

# DiMO Testing Configuration
# This config is for testing the distilled one-step generator

name: DiMO_test
model_type: DiMO
scale: 1
num_gpu: 1

# Dataset configuration
datasets:
  test:
    name: PolarizationTest
    type: PolarizationDataset
    dataroot_lq: datasets/test/lq
    dataroot_gt: datasets/test/gt  # optional, for metrics calculation
    io_backend:
      type: disk

# Network structures (same as training)
network_g:
  type: InvNet
  embed_dim: 64
  group: 4
  block_num: 6
  patch_expansion: 0.5
  channel_expansion: 4

network_le:
  type: latent_encoder_gelu
  in_chans: 12
  embed_dim: 64
  block_num: 6
  group: 4
  stage: 1
  patch_expansion: 0.5
  channel_expansion: 4

network_le_dm:
  type: latent_encoder_gelu
  in_chans: 9
  embed_dim: 64
  block_num: 6
  group: 4
  stage: 2
  patch_expansion: 0.5
  channel_expansion: 4

network_d:
  type: denoising
  in_channel: 256
  out_channel: 256
  inner_channel: 512
  block_num: 4
  group: 4
  patch_expansion: 0.5
  channel_expansion: 2

# DiMO Student Network
network_student:
  type: dimo_student
  input_dim: 256
  prior_dim: 256
  output_dim: 256
  hidden_dim: 512
  num_layers: 6
  num_heads: 8
  group: 4
  patch_expansion: 0.5
  channel_expansion: 4
  use_attention: true
  dropout: 0.1

# Diffusion schedule (for teacher model, not used in inference)
diffusion_schedule:
  apply_ldm: false
  schedule: linear
  timesteps: 8
  linear_start: 0.1
  linear_end: 0.99

# Path configuration
path:
  pretrain_network_g: experiments/DiMO_distillation/models/net_g_latest.pth
  strict_load_g: true

# Validation settings
val:
  save_img: true
  suffix: ~  # add suffix to saved images, if None, use exp name

  metrics:
    psnr: # metric name, can be arbitrary
      type: calculate_psnr
      crop_border: 0
      test_y_channel: false
    ssim:
      type: calculate_ssim
      crop_border: 0
      test_y_channel: false

# Distributed testing settings
dist_params:
  backend: nccl
  port: 29500

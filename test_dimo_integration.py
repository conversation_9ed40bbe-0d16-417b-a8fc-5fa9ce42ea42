#!/usr/bin/env python3
"""
DiMO Integration Test
Test the DiMO implementation to ensure all components work correctly.
"""

import torch
import numpy as np
import sys
import os
from os import path as osp

# Add current directory to path
sys.path.insert(0, osp.dirname(osp.abspath(__file__)))

def test_student_network():
    """Test DiMO student network."""
    print("Testing DiMO student network...")
    
    try:
        from polarfree.archs.dimo_student_arch import dimo_student
        
        # Create student network
        student = dimo_student(
            input_dim=256,
            prior_dim=256,
            output_dim=256,
            hidden_dim=512,
            num_layers=4,
            group=4
        )
        
        # Test forward pass
        batch_size, seq_len = 2, 16
        input_features = torch.randn(batch_size, seq_len, 256)
        prior_features = torch.randn(batch_size, seq_len, 256)
        
        with torch.no_grad():
            output = student(input_features, prior_features)
        
        assert output.shape == (batch_size, seq_len, 256), f"Expected shape {(batch_size, seq_len, 256)}, got {output.shape}"
        print("✓ Student network test passed")
        return True
        
    except Exception as e:
        print(f"✗ Student network test failed: {str(e)}")
        return False

def test_loss_functions():
    """Test DiMO loss functions."""
    print("Testing DiMO loss functions...")
    
    try:
        from polarfree.losses.dimo_loss import DiMODistillationLoss, DiMOCombinedLoss
        
        # Test distillation loss
        distill_loss = DiMODistillationLoss(loss_type='mse')
        
        batch_size, seq_len, dim = 2, 16, 256
        student_output = torch.randn(batch_size, seq_len, dim)
        teacher_output = torch.randn(batch_size, seq_len, dim)
        
        loss_value = distill_loss(student_output, teacher_output)
        assert loss_value.item() >= 0, "Loss should be non-negative"
        
        # Test combined loss
        combined_loss = DiMOCombinedLoss()
        total_loss, loss_dict = combined_loss(student_output, teacher_output)
        assert total_loss.item() >= 0, "Total loss should be non-negative"
        assert 'distillation' in loss_dict, "Loss dict should contain distillation loss"
        
        print("✓ Loss functions test passed")
        return True
        
    except Exception as e:
        print(f"✗ Loss functions test failed: {str(e)}")
        return False

def test_dimo_model():
    """Test DiMO model class."""
    print("Testing DiMO model class...")
    
    try:
        # Create minimal config for testing
        opt = {
            'model_type': 'DiMO',
            'num_gpu': 0,  # Use CPU for testing
            'is_train': True,
            'dist': False,
            'network_g': {
                'type': 'InvNet',
                'embed_dim': 64,
                'group': 4,
                'block_num': 6,
                'patch_expansion': 0.5,
                'channel_expansion': 4
            },
            'network_le': {
                'type': 'latent_encoder_gelu',
                'in_chans': 12,
                'embed_dim': 64,
                'block_num': 6,
                'group': 4,
                'stage': 1,
                'patch_expansion': 0.5,
                'channel_expansion': 4
            },
            'network_le_dm': {
                'type': 'latent_encoder_gelu',
                'in_chans': 9,
                'embed_dim': 64,
                'block_num': 6,
                'group': 4,
                'stage': 2,
                'patch_expansion': 0.5,
                'channel_expansion': 4
            },
            'network_d': {
                'type': 'denoising',
                'in_channel': 256,
                'out_channel': 256,
                'inner_channel': 512,
                'block_num': 4,
                'group': 4,
                'patch_expansion': 0.5,
                'channel_expansion': 2
            },
            'network_student': {
                'type': 'dimo_student',
                'input_dim': 256,
                'prior_dim': 256,
                'output_dim': 256,
                'hidden_dim': 256,
                'num_layers': 3,
                'group': 4,
                'patch_expansion': 0.5,
                'channel_expansion': 2
            },
            'diffusion_schedule': {
                'apply_ldm': False,
                'schedule': 'linear',
                'timesteps': 8,
                'linear_start': 0.1,
                'linear_end': 0.99
            },
            'path': {
                'pretrain_network_g': None,
                'pretrain_network_le': None,
                'pretrain_teacher': None
            },
            'train': {
                'optim_g': {
                    'type': 'Adam',
                    'lr': 2e-4,
                    'weight_decay': 0,
                    'betas': [0.9, 0.99]
                },
                'distillation_loss_weight': 1.0,
                'distillation_loss': {
                    'type': 'DiMODistillationLoss',
                    'loss_type': 'mse'
                },
                'pixel_loss_weight': 0.1,
                'pixel_loss': {
                    'type': 'L1Loss'
                },
                'perceptual_loss_weight': 0,
                'style_loss_weight': 0
            }
        }
        
        # This test would require the full model infrastructure
        # For now, just test that the import works
        from polarfree.models.DiMO_model import DiMO
        print("✓ DiMO model import test passed")
        return True
        
    except Exception as e:
        print(f"✗ DiMO model test failed: {str(e)}")
        return False

def test_config_files():
    """Test configuration files."""
    print("Testing configuration files...")
    
    try:
        import yaml
        
        # Test training config
        train_config_path = 'options/train/dimo_distillation.yml'
        if osp.exists(train_config_path):
            with open(train_config_path, 'r') as f:
                train_config = yaml.safe_load(f)
            assert 'network_student' in train_config, "Training config should have student network"
            assert 'distillation_loss' in train_config['train'], "Training config should have distillation loss"
        
        # Test testing config
        test_config_path = 'options/test/dimo_test.yml'
        if osp.exists(test_config_path):
            with open(test_config_path, 'r') as f:
                test_config = yaml.safe_load(f)
            assert 'network_student' in test_config, "Test config should have student network"
        
        print("✓ Configuration files test passed")
        return True
        
    except Exception as e:
        print(f"✗ Configuration files test failed: {str(e)}")
        return False

def test_scripts():
    """Test that scripts exist and are executable."""
    print("Testing scripts...")
    
    try:
        scripts = [
            'train_dimo.py',
            'test_dimo.py',
            'demo_dimo.py',
            'compare_dimo.py',
            'train_dimo.sh'
        ]
        
        for script in scripts:
            if not osp.exists(script):
                print(f"✗ Script {script} not found")
                return False
        
        print("✓ Scripts test passed")
        return True
        
    except Exception as e:
        print(f"✗ Scripts test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("=" * 50)
    print("DiMO Integration Test Suite")
    print("=" * 50)
    
    tests = [
        test_student_network,
        test_loss_functions,
        test_dimo_model,
        test_config_files,
        test_scripts
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
        print()
    
    print("=" * 50)
    print(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! DiMO implementation is ready.")
        return True
    else:
        print("❌ Some tests failed. Please check the implementation.")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
